<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cms_pages', function (Blueprint $table) {
            // Make content nullable since Puck pages don't use it
            $table->longText('content')->nullable()->change();

            // Add Puck-specific fields
            $table->json('puck_data')->nullable()->after('content')
                ->comment('Puck visual editor JSON data structure');

            $table->enum('editor_type', ['html', 'puck'])->default('html')->after('puck_data')
                ->comment('Type of editor used: html for traditional editor, puck for visual editor');

            $table->text('rendered_content')->nullable()->after('editor_type')
                ->comment('Rendered HTML content from Puck data for public display and SEO');

            $table->json('puck_meta')->nullable()->after('rendered_content')
                ->comment('Additional Puck metadata like component versions, theme settings, etc.');

            // Add index for editor type for efficient filtering
            $table->index('editor_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cms_pages', function (Blueprint $table) {
            $table->dropIndex(['editor_type']);
            $table->dropColumn([
                'puck_data',
                'editor_type',
                'rendered_content',
                'puck_meta'
            ]);

            // Revert content field back to NOT NULL
            $table->longText('content')->nullable(false)->change();
        });
    }
};
