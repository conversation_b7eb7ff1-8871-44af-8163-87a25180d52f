<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\CmsPage;
use Laravel\Sanctum\Sanctum;

class PuckCmsIntegrationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $adminUser;
    protected User $regularUser;

    protected function setUp(): void
    {
        parent::setUp();

        $this->adminUser = User::factory()->create([
            'role' => 'admin',
            'is_active' => true,
        ]);

        $this->regularUser = User::factory()->create([
            'role' => 'user',
            'is_active' => true,
        ]);
    }

    /** @test */
    public function it_can_create_page_with_puck_editor()
    {
        Sanctum::actingAs($this->adminUser);

        $puckData = [
            'content' => [
                [
                    'type' => 'Hero',
                    'props' => [
                        'id' => 'hero-123',
                        'title' => 'Welcome to Our Site',
                        'subtitle' => 'This is a great place to be',
                        'textAlign' => 'center',
                        'minHeight' => '400px',
                    ]
                ]
            ],
            'root' => [
                'title' => 'Test Puck Page',
                'metaDescription' => 'A test page created with Puck',
            ]
        ];

        $response = $this->postJson('/api/admin/cms-pages', [
            'title' => 'Test Puck Page',
            'editor_type' => 'puck',
            'puck_data' => $puckData,
            'is_published' => true,
        ]);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'page' => [
                        'id',
                        'title',
                        'editor_type',
                        'puck_data',
                        'rendered_content',
                        'content',
                    ]
                ]
            ]);

        $this->assertTrue($response->json('success'));
        $this->assertEquals('puck', $response->json('data.page.editor_type'));
        $this->assertNotNull($response->json('data.page.puck_data'));
        $this->assertNotNull($response->json('data.page.rendered_content'));

        // Verify in database
        $this->assertDatabaseHas('cms_pages', [
            'title' => 'Test Puck Page',
            'editor_type' => 'puck',
        ]);
    }

    /** @test */
    public function it_can_create_page_with_html_editor()
    {
        Sanctum::actingAs($this->adminUser);

        $response = $this->postJson('/api/admin/cms-pages', [
            'title' => 'Test HTML Page',
            'editor_type' => 'html',
            'content' => '<h1>Welcome</h1><p>This is HTML content</p>',
            'is_published' => true,
        ]);

        $response->assertStatus(201);
        $this->assertEquals('html', $response->json('data.page.editor_type'));
        $this->assertNull($response->json('data.page.puck_data'));

        // Verify in database
        $this->assertDatabaseHas('cms_pages', [
            'title' => 'Test HTML Page',
            'editor_type' => 'html',
        ]);
    }

    /** @test */
    public function it_validates_puck_data_structure()
    {
        Sanctum::actingAs($this->adminUser);

        // Invalid Puck data - missing required id in props
        $invalidPuckData = [
            'content' => [
                [
                    'type' => 'Hero',
                    'props' => [
                        'title' => 'Welcome',
                        // Missing required 'id' field
                    ]
                ]
            ]
        ];

        $response = $this->postJson('/api/admin/cms-pages', [
            'title' => 'Test Page',
            'editor_type' => 'puck',
            'puck_data' => $invalidPuckData,
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['puck_data.content.0.props.id']);
    }

    /** @test */
    public function it_can_get_puck_data_for_existing_page()
    {
        Sanctum::actingAs($this->adminUser);

        $puckData = [
            'content' => [
                [
                    'type' => 'Text',
                    'props' => [
                        'id' => 'text-123',
                        'content' => 'Hello World',
                    ]
                ]
            ]
        ];

        $page = CmsPage::create([
            'title' => 'Test Page',
            'editor_type' => 'puck',
            'puck_data' => $puckData,
            'rendered_content' => '<div>Hello World</div>',
            'content' => '<div>Hello World</div>',
            'created_by' => $this->adminUser->id,
            'updated_by' => $this->adminUser->id,
        ]);

        $response = $this->getJson("/api/admin/cms-pages/{$page->id}/puck-data");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'page',
                    'puck_data',
                    'editor_type',
                ]
            ]);

        $this->assertTrue($response->json('success'));
        $this->assertEquals('puck', $response->json('data.editor_type'));
        $this->assertNotEmpty($response->json('data.puck_data'));
    }

    /** @test */
    public function it_can_save_puck_data_for_existing_page()
    {
        Sanctum::actingAs($this->adminUser);

        $page = CmsPage::create([
            'title' => 'Test Page',
            'editor_type' => 'puck',
            'content' => '<p>Original content</p>',
            'created_by' => $this->adminUser->id,
            'updated_by' => $this->adminUser->id,
        ]);

        $newPuckData = [
            'content' => [
                [
                    'type' => 'Hero',
                    'props' => [
                        'id' => 'hero-456',
                        'title' => 'Updated Title',
                        'subtitle' => 'Updated subtitle',
                    ]
                ]
            ]
        ];

        $response = $this->postJson("/api/admin/cms-pages/{$page->id}/puck-data", [
            'puck_data' => $newPuckData,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Puck data saved successfully',
            ]);

        // Verify page was updated
        $page->refresh();
        $this->assertEquals($newPuckData, $page->puck_data);
        $this->assertNotNull($page->rendered_content);
    }

    /** @test */
    public function it_can_convert_html_to_puck_format()
    {
        Sanctum::actingAs($this->adminUser);

        $htmlContent = '<h1>Welcome</h1><p>This is a paragraph</p>';

        $response = $this->postJson('/api/admin/cms-pages/convert/html-to-puck', [
            'content' => $htmlContent,
            'title' => 'Test Page',
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'puck_data' => [
                        'content',
                        'root',
                    ]
                ]
            ]);

        $this->assertTrue($response->json('success'));
        $puckData = $response->json('data.puck_data');
        $this->assertIsArray($puckData['content']);
        $this->assertNotEmpty($puckData['content']);

        // Verify components have IDs
        foreach ($puckData['content'] as $component) {
            $this->assertArrayHasKey('props', $component);
            $this->assertArrayHasKey('id', $component['props']);
        }
    }

    /** @test */
    public function it_can_convert_puck_to_html_format()
    {
        Sanctum::actingAs($this->adminUser);

        $puckData = [
            'content' => [
                [
                    'type' => 'Hero',
                    'props' => [
                        'id' => 'hero-789',
                        'title' => 'Test Hero',
                        'subtitle' => 'Test subtitle',
                        'textAlign' => 'center',
                    ]
                ]
            ]
        ];

        $response = $this->postJson('/api/admin/cms-pages/convert/puck-to-html', [
            'puck_data' => $puckData,
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'html',
                    'seo_data',
                ]
            ]);

        $this->assertTrue($response->json('success'));
        $html = $response->json('data.html');
        $this->assertStringContainsString('Test Hero', $html);
        $this->assertStringContainsString('Test subtitle', $html);
    }

    /** @test */
    public function it_can_switch_editor_type_from_html_to_puck()
    {
        Sanctum::actingAs($this->adminUser);

        $page = CmsPage::create([
            'title' => 'HTML Page',
            'editor_type' => 'html',
            'content' => '<h1>HTML Content</h1><p>This is HTML</p>',
            'created_by' => $this->adminUser->id,
            'updated_by' => $this->adminUser->id,
        ]);

        $response = $this->postJson("/api/admin/cms-pages/{$page->id}/switch-editor", [
            'editor_type' => 'puck',
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Editor type switched to puck successfully',
            ]);

        // Verify page was updated
        $page->refresh();
        $this->assertEquals('puck', $page->editor_type);
        $this->assertNotNull($page->puck_data);
        $this->assertNotNull($page->rendered_content);
    }

    /** @test */
    public function it_can_switch_editor_type_from_puck_to_html()
    {
        Sanctum::actingAs($this->adminUser);

        $puckData = [
            'content' => [
                [
                    'type' => 'Text',
                    'props' => [
                        'id' => 'text-999',
                        'content' => 'Puck content',
                    ]
                ]
            ]
        ];

        $page = CmsPage::create([
            'title' => 'Puck Page',
            'editor_type' => 'puck',
            'puck_data' => $puckData,
            'rendered_content' => '<div>Puck content</div>',
            'content' => '<div>Puck content</div>',
            'created_by' => $this->adminUser->id,
            'updated_by' => $this->adminUser->id,
        ]);

        $response = $this->postJson("/api/admin/cms-pages/{$page->id}/switch-editor", [
            'editor_type' => 'html',
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Editor type switched to html successfully',
            ]);

        // Verify page was updated
        $page->refresh();
        $this->assertEquals('html', $page->editor_type);
        $this->assertNull($page->puck_data);
        $this->assertNull($page->rendered_content);
        $this->assertNotNull($page->content);
    }

    /** @test */
    public function it_validates_puck_data_endpoint()
    {
        Sanctum::actingAs($this->adminUser);

        $validPuckData = [
            'content' => [
                [
                    'type' => 'Hero',
                    'props' => [
                        'id' => 'hero-valid',
                        'title' => 'Valid Hero',
                    ]
                ]
            ]
        ];

        $response = $this->postJson('/api/admin/cms-pages/validate-puck-data', [
            'puck_data' => $validPuckData,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Puck data is valid',
                'data' => [
                    'is_valid' => true,
                ]
            ]);
    }

    /** @test */
    public function it_rejects_invalid_puck_data_in_validation_endpoint()
    {
        Sanctum::actingAs($this->adminUser);

        $invalidPuckData = [
            'content' => [
                [
                    'type' => 'Hero',
                    'props' => [
                        // Missing required 'id' field
                        'title' => 'Invalid Hero',
                    ]
                ]
            ]
        ];

        $response = $this->postJson('/api/admin/cms-pages/validate-puck-data', [
            'puck_data' => $invalidPuckData,
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['puck_data.content.0.props.id']);
    }

    /** @test */
    public function it_maintains_backward_compatibility_with_existing_html_pages()
    {
        // Create an HTML page the old way
        $page = CmsPage::create([
            'title' => 'Legacy HTML Page',
            'content' => '<h1>Legacy Content</h1>',
            'created_by' => $this->adminUser->id,
            'updated_by' => $this->adminUser->id,
            // editor_type will default to 'html'
        ]);

        // Verify it works with public API
        $response = $this->getJson("/api/pages/{$page->slug}");
        $response->assertStatus(200);

        // Verify display content works
        $this->assertEquals('<h1>Legacy Content</h1>', $page->getDisplayContent());
        $this->assertTrue($page->isHtmlPage());
        $this->assertFalse($page->isPuckPage());
    }

    /** @test */
    public function regular_user_cannot_access_puck_endpoints()
    {
        Sanctum::actingAs($this->regularUser);

        $endpoints = [
            'GET /api/admin/cms-pages/1/puck-data',
            'POST /api/admin/cms-pages/1/puck-data',
            'POST /api/admin/cms-pages/1/switch-editor',
            'POST /api/admin/cms-pages/convert/html-to-puck',
            'POST /api/admin/cms-pages/convert/puck-to-html',
            'POST /api/admin/cms-pages/validate-puck-data',
        ];

        foreach ($endpoints as $endpoint) {
            [$method, $url] = explode(' ', $endpoint);
            
            $response = $this->json($method, $url, [
                'puck_data' => ['content' => []],
                'editor_type' => 'puck',
                'content' => 'test',
            ]);

            $response->assertStatus(403)
                ->assertJson([
                    'success' => false,
                    'message' => 'Unauthorized. Admin access required.'
                ]);
        }
    }
}
