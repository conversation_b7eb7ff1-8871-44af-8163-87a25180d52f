{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\frontend\\\\src\\\\components\\\\puck\\\\PuckEditor.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Puck } from '@measured/puck';\nimport '@measured/puck/puck.css';\nimport './PuckEditor.css';\nimport { useNavigate } from 'react-router-dom';\nimport { Container, <PERSON><PERSON>, <PERSON><PERSON>, But<PERSON> } from 'react-bootstrap';\nimport authService from '../../services/authService';\nimport { suppressResizeObserverErrors } from '../../utils/errorHandlers';\n\n// Comprehensive Puck configuration with all essential components\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst puckConfig = {\n  components: {\n    // Layout Components\n    Hero: {\n      label: 'Hero Section',\n      defaultProps: {\n        title: 'Hero Title',\n        subtitle: 'Hero subtitle text',\n        textAlign: 'center',\n        minHeight: '400px',\n        backgroundType: 'gradient',\n        backgroundColor: '#667eea',\n        backgroundGradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        textColor: '#ffffff'\n      },\n      fields: {\n        title: {\n          type: 'text',\n          label: 'Title'\n        },\n        subtitle: {\n          type: 'textarea',\n          label: 'Subtitle'\n        },\n        textAlign: {\n          type: 'select',\n          label: 'Text Alignment',\n          options: [{\n            label: 'Left',\n            value: 'left'\n          }, {\n            label: 'Center',\n            value: 'center'\n          }, {\n            label: 'Right',\n            value: 'right'\n          }]\n        },\n        minHeight: {\n          type: 'text',\n          label: 'Min Height (px)'\n        },\n        backgroundType: {\n          type: 'select',\n          label: 'Background Type',\n          options: [{\n            label: 'Solid Color',\n            value: 'solid'\n          }, {\n            label: 'Gradient',\n            value: 'gradient'\n          }, {\n            label: 'Image',\n            value: 'image'\n          }]\n        },\n        backgroundColor: {\n          type: 'text',\n          label: 'Background Color'\n        },\n        backgroundGradient: {\n          type: 'text',\n          label: 'Background Gradient'\n        },\n        backgroundImage: {\n          type: 'text',\n          label: 'Background Image URL'\n        },\n        textColor: {\n          type: 'text',\n          label: 'Text Color'\n        }\n      },\n      render: ({\n        title,\n        subtitle,\n        textAlign,\n        minHeight,\n        backgroundType,\n        backgroundColor,\n        backgroundGradient,\n        backgroundImage,\n        textColor\n      }) => {\n        let backgroundStyle = {};\n        switch (backgroundType) {\n          case 'solid':\n            backgroundStyle = {\n              backgroundColor: backgroundColor || '#667eea'\n            };\n            break;\n          case 'gradient':\n            backgroundStyle = {\n              background: backgroundGradient || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\n            };\n            break;\n          case 'image':\n            backgroundStyle = {\n              backgroundImage: `url(${backgroundImage})`,\n              backgroundSize: 'cover',\n              backgroundPosition: 'center',\n              backgroundRepeat: 'no-repeat'\n            };\n            break;\n          default:\n            backgroundStyle = {\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\n            };\n        }\n        return /*#__PURE__*/_jsxDEV(\"section\", {\n          style: {\n            minHeight: minHeight || '400px',\n            display: 'flex',\n            flexDirection: 'column',\n            justifyContent: 'center',\n            alignItems: textAlign === 'center' ? 'center' : textAlign === 'right' ? 'flex-end' : 'flex-start',\n            textAlign: textAlign || 'center',\n            padding: '4rem 2rem',\n            color: textColor || 'white',\n            position: 'relative',\n            ...backgroundStyle\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'relative',\n              zIndex: 1,\n              maxWidth: '800px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              style: {\n                fontSize: 'clamp(2rem, 5vw, 3.5rem)',\n                marginBottom: '1rem',\n                fontWeight: 'bold',\n                lineHeight: 1.2\n              },\n              children: title || 'Hero Title'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                fontSize: 'clamp(1rem, 2.5vw, 1.25rem)',\n                opacity: 0.9,\n                lineHeight: 1.6,\n                marginBottom: '2rem'\n              },\n              children: subtitle || 'Hero subtitle text'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this);\n      }\n    },\n    // Content Components\n    Text: {\n      label: 'Text Block',\n      defaultProps: {\n        content: '<p>Enter your text content here...</p>',\n        textAlign: 'left',\n        fontSize: '16px',\n        color: '#333333',\n        lineHeight: '1.6',\n        marginTop: '0px',\n        marginBottom: '20px'\n      },\n      fields: {\n        content: {\n          type: 'textarea',\n          label: 'Content (HTML)'\n        },\n        textAlign: {\n          type: 'select',\n          label: 'Text Alignment',\n          options: [{\n            label: 'Left',\n            value: 'left'\n          }, {\n            label: 'Center',\n            value: 'center'\n          }, {\n            label: 'Right',\n            value: 'right'\n          }, {\n            label: 'Justify',\n            value: 'justify'\n          }]\n        },\n        fontSize: {\n          type: 'text',\n          label: 'Font Size'\n        },\n        color: {\n          type: 'text',\n          label: 'Text Color'\n        },\n        lineHeight: {\n          type: 'text',\n          label: 'Line Height'\n        },\n        marginTop: {\n          type: 'text',\n          label: 'Margin Top'\n        },\n        marginBottom: {\n          type: 'text',\n          label: 'Margin Bottom'\n        }\n      },\n      render: ({\n        content,\n        textAlign,\n        fontSize,\n        color,\n        lineHeight,\n        marginTop,\n        marginBottom\n      }) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-component\",\n        style: {\n          textAlign: textAlign || 'left',\n          fontSize: fontSize || '16px',\n          color: color || '#333333',\n          lineHeight: lineHeight || '1.6',\n          marginTop: marginTop || '0px',\n          marginBottom: marginBottom || '20px',\n          padding: '0 1rem'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          dangerouslySetInnerHTML: {\n            __html: content || '<p>Enter your text content here...</p>'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this)\n    },\n    Heading: {\n      label: 'Heading',\n      defaultProps: {\n        text: 'Heading Text',\n        level: 'h2',\n        textAlign: 'left',\n        color: '#333333',\n        marginTop: '0px',\n        marginBottom: '20px'\n      },\n      fields: {\n        text: {\n          type: 'text',\n          label: 'Heading Text'\n        },\n        level: {\n          type: 'select',\n          label: 'Heading Level',\n          options: [{\n            label: 'H1',\n            value: 'h1'\n          }, {\n            label: 'H2',\n            value: 'h2'\n          }, {\n            label: 'H3',\n            value: 'h3'\n          }, {\n            label: 'H4',\n            value: 'h4'\n          }, {\n            label: 'H5',\n            value: 'h5'\n          }, {\n            label: 'H6',\n            value: 'h6'\n          }]\n        },\n        textAlign: {\n          type: 'select',\n          label: 'Text Alignment',\n          options: [{\n            label: 'Left',\n            value: 'left'\n          }, {\n            label: 'Center',\n            value: 'center'\n          }, {\n            label: 'Right',\n            value: 'right'\n          }]\n        },\n        color: {\n          type: 'text',\n          label: 'Text Color'\n        },\n        marginTop: {\n          type: 'text',\n          label: 'Margin Top'\n        },\n        marginBottom: {\n          type: 'text',\n          label: 'Margin Bottom'\n        }\n      },\n      render: ({\n        text,\n        level,\n        textAlign,\n        color,\n        marginTop,\n        marginBottom\n      }) => {\n        const HeadingTag = level || 'h2';\n        const fontSizes = {\n          h1: '2.5rem',\n          h2: '2rem',\n          h3: '1.75rem',\n          h4: '1.5rem',\n          h5: '1.25rem',\n          h6: '1rem'\n        };\n        const fontSize = fontSizes[level] || '2rem';\n        return /*#__PURE__*/React.createElement(HeadingTag, {\n          style: {\n            textAlign: textAlign || 'left',\n            color: color || '#333333',\n            fontSize,\n            fontWeight: 'bold',\n            marginTop: marginTop || '0px',\n            marginBottom: marginBottom || '20px',\n            padding: '0 1rem',\n            lineHeight: 1.2\n          }\n        }, text || 'Heading Text');\n      }\n    },\n    Button: {\n      label: 'Button',\n      defaultProps: {\n        text: 'Button Text',\n        href: '#',\n        variant: 'primary',\n        size: 'md',\n        textAlign: 'center',\n        fullWidth: false,\n        target: '_self'\n      },\n      fields: {\n        text: {\n          type: 'text',\n          label: 'Button Text'\n        },\n        href: {\n          type: 'text',\n          label: 'Link URL'\n        },\n        variant: {\n          type: 'select',\n          label: 'Button Style',\n          options: [{\n            label: 'Primary',\n            value: 'primary'\n          }, {\n            label: 'Secondary',\n            value: 'secondary'\n          }, {\n            label: 'Success',\n            value: 'success'\n          }, {\n            label: 'Danger',\n            value: 'danger'\n          }, {\n            label: 'Warning',\n            value: 'warning'\n          }, {\n            label: 'Info',\n            value: 'info'\n          }, {\n            label: 'Light',\n            value: 'light'\n          }, {\n            label: 'Dark',\n            value: 'dark'\n          }]\n        },\n        size: {\n          type: 'select',\n          label: 'Button Size',\n          options: [{\n            label: 'Small',\n            value: 'sm'\n          }, {\n            label: 'Medium',\n            value: 'md'\n          }, {\n            label: 'Large',\n            value: 'lg'\n          }]\n        },\n        textAlign: {\n          type: 'select',\n          label: 'Alignment',\n          options: [{\n            label: 'Left',\n            value: 'left'\n          }, {\n            label: 'Center',\n            value: 'center'\n          }, {\n            label: 'Right',\n            value: 'right'\n          }]\n        },\n        fullWidth: {\n          type: 'radio',\n          label: 'Full Width',\n          options: [{\n            label: 'Yes',\n            value: true\n          }, {\n            label: 'No',\n            value: false\n          }]\n        },\n        target: {\n          type: 'select',\n          label: 'Link Target',\n          options: [{\n            label: 'Same Window',\n            value: '_self'\n          }, {\n            label: 'New Window',\n            value: '_blank'\n          }]\n        }\n      },\n      render: ({\n        text,\n        href,\n        variant,\n        size,\n        textAlign,\n        fullWidth,\n        target\n      }) => {\n        const colors = {\n          primary: '#007bff',\n          secondary: '#6c757d',\n          success: '#28a745',\n          danger: '#dc3545',\n          warning: '#ffc107',\n          info: '#17a2b8',\n          light: '#f8f9fa',\n          dark: '#343a40'\n        };\n        const textColors = {\n          primary: 'white',\n          secondary: 'white',\n          success: 'white',\n          danger: 'white',\n          warning: '#212529',\n          info: 'white',\n          light: '#212529',\n          dark: 'white'\n        };\n        const padding = size === 'sm' ? '0.5rem 1rem' : size === 'lg' ? '1rem 2rem' : '0.75rem 1.5rem';\n        const fontSize = size === 'sm' ? '0.875rem' : size === 'lg' ? '1.125rem' : '1rem';\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '1rem',\n            textAlign: textAlign || 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: href || '#',\n            target: target || '_self',\n            rel: target === '_blank' ? 'noopener noreferrer' : undefined,\n            style: {\n              display: fullWidth ? 'block' : 'inline-block',\n              width: fullWidth ? '100%' : 'auto',\n              padding,\n              backgroundColor: colors[variant] || colors.primary,\n              color: textColors[variant] || 'white',\n              textDecoration: 'none',\n              borderRadius: '0.375rem',\n              fontWeight: '500',\n              fontSize,\n              textAlign: 'center',\n              transition: 'all 0.2s ease-in-out',\n              border: 'none',\n              cursor: 'pointer'\n            },\n            onMouseOver: e => {\n              e.currentTarget.style.opacity = '0.9';\n              e.currentTarget.style.transform = 'translateY(-1px)';\n            },\n            onMouseOut: e => {\n              e.currentTarget.style.opacity = '1';\n              e.currentTarget.style.transform = 'translateY(0)';\n            },\n            children: text || 'Button Text'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this);\n      }\n    },\n    Image: {\n      label: 'Image',\n      defaultProps: {\n        src: 'https://via.placeholder.com/600x400',\n        alt: 'Image description',\n        width: '100%',\n        height: 'auto',\n        objectFit: 'cover',\n        borderRadius: '0px',\n        marginTop: '0px',\n        marginBottom: '20px'\n      },\n      fields: {\n        src: {\n          type: 'text',\n          label: 'Image URL'\n        },\n        alt: {\n          type: 'text',\n          label: 'Alt Text'\n        },\n        width: {\n          type: 'text',\n          label: 'Width'\n        },\n        height: {\n          type: 'text',\n          label: 'Height'\n        },\n        objectFit: {\n          type: 'select',\n          label: 'Object Fit',\n          options: [{\n            label: 'Cover',\n            value: 'cover'\n          }, {\n            label: 'Contain',\n            value: 'contain'\n          }, {\n            label: 'Fill',\n            value: 'fill'\n          }, {\n            label: 'None',\n            value: 'none'\n          }, {\n            label: 'Scale Down',\n            value: 'scale-down'\n          }]\n        },\n        borderRadius: {\n          type: 'text',\n          label: 'Border Radius'\n        },\n        marginTop: {\n          type: 'text',\n          label: 'Margin Top'\n        },\n        marginBottom: {\n          type: 'text',\n          label: 'Margin Bottom'\n        }\n      },\n      render: ({\n        src,\n        alt,\n        width,\n        height,\n        objectFit,\n        borderRadius,\n        marginTop,\n        marginBottom\n      }) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '1rem',\n          marginTop: marginTop || '0px',\n          marginBottom: marginBottom || '20px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: src || 'https://via.placeholder.com/600x400',\n          alt: alt || 'Image description',\n          style: {\n            width: width || '100%',\n            height: height || 'auto',\n            objectFit: objectFit || 'cover',\n            borderRadius: borderRadius || '0px',\n            display: 'block',\n            maxWidth: '100%'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 9\n      }, this)\n    },\n    Container: {\n      label: 'Container',\n      defaultProps: {\n        maxWidth: 'lg',\n        padding: '2rem',\n        backgroundColor: 'transparent',\n        borderRadius: '0px',\n        marginTop: '0px',\n        marginBottom: '0px'\n      },\n      fields: {\n        maxWidth: {\n          type: 'select',\n          label: 'Max Width',\n          options: [{\n            label: 'Small (540px)',\n            value: 'sm'\n          }, {\n            label: 'Medium (720px)',\n            value: 'md'\n          }, {\n            label: 'Large (960px)',\n            value: 'lg'\n          }, {\n            label: 'Extra Large (1140px)',\n            value: 'xl'\n          }, {\n            label: 'Full Width',\n            value: 'fluid'\n          }]\n        },\n        padding: {\n          type: 'text',\n          label: 'Padding'\n        },\n        backgroundColor: {\n          type: 'text',\n          label: 'Background Color'\n        },\n        borderRadius: {\n          type: 'text',\n          label: 'Border Radius'\n        },\n        marginTop: {\n          type: 'text',\n          label: 'Margin Top'\n        },\n        marginBottom: {\n          type: 'text',\n          label: 'Margin Bottom'\n        }\n      },\n      render: ({\n        maxWidth,\n        padding,\n        backgroundColor,\n        borderRadius,\n        marginTop,\n        marginBottom,\n        children\n      }) => {\n        const maxWidths = {\n          sm: '540px',\n          md: '720px',\n          lg: '960px',\n          xl: '1140px',\n          fluid: '100%'\n        };\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            maxWidth: maxWidths[maxWidth] || '960px',\n            margin: '0 auto',\n            padding: padding || '2rem',\n            backgroundColor: backgroundColor || 'transparent',\n            borderRadius: borderRadius || '0px',\n            marginTop: marginTop || '0px',\n            marginBottom: marginBottom || '0px'\n          },\n          children: children\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 11\n        }, this);\n      }\n    },\n    Spacer: {\n      label: 'Spacer',\n      defaultProps: {\n        height: '40px'\n      },\n      fields: {\n        height: {\n          type: 'text',\n          label: 'Height'\n        }\n      },\n      render: ({\n        height\n      }) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          height: height || '40px',\n          width: '100%'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 459,\n        columnNumber: 9\n      }, this)\n    },\n    Divider: {\n      label: 'Divider',\n      defaultProps: {\n        color: '#e0e0e0',\n        thickness: '1px',\n        style: 'solid',\n        marginTop: '20px',\n        marginBottom: '20px'\n      },\n      fields: {\n        color: {\n          type: 'text',\n          label: 'Color'\n        },\n        thickness: {\n          type: 'text',\n          label: 'Thickness'\n        },\n        style: {\n          type: 'select',\n          label: 'Style',\n          options: [{\n            label: 'Solid',\n            value: 'solid'\n          }, {\n            label: 'Dashed',\n            value: 'dashed'\n          }, {\n            label: 'Dotted',\n            value: 'dotted'\n          }]\n        },\n        marginTop: {\n          type: 'text',\n          label: 'Margin Top'\n        },\n        marginBottom: {\n          type: 'text',\n          label: 'Margin Bottom'\n        }\n      },\n      render: ({\n        color,\n        thickness,\n        style,\n        marginTop,\n        marginBottom\n      }) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '0 1rem',\n          marginTop: marginTop || '20px',\n          marginBottom: marginBottom || '20px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"hr\", {\n          style: {\n            border: 'none',\n            borderTop: `${thickness || '1px'} ${style || 'solid'} ${color || '#e0e0e0'}`,\n            margin: 0\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 489,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 488,\n        columnNumber: 9\n      }, this)\n    },\n    Card: {\n      label: 'Card',\n      defaultProps: {\n        title: 'Card Title',\n        content: '<p>Card content goes here...</p>',\n        imageUrl: '',\n        backgroundColor: '#ffffff',\n        borderColor: '#e0e0e0',\n        borderRadius: '8px',\n        padding: '1.5rem',\n        shadow: true\n      },\n      fields: {\n        title: {\n          type: 'text',\n          label: 'Card Title'\n        },\n        content: {\n          type: 'textarea',\n          label: 'Card Content (HTML)'\n        },\n        imageUrl: {\n          type: 'text',\n          label: 'Image URL (optional)'\n        },\n        backgroundColor: {\n          type: 'text',\n          label: 'Background Color'\n        },\n        borderColor: {\n          type: 'text',\n          label: 'Border Color'\n        },\n        borderRadius: {\n          type: 'text',\n          label: 'Border Radius'\n        },\n        padding: {\n          type: 'text',\n          label: 'Padding'\n        },\n        shadow: {\n          type: 'radio',\n          label: 'Drop Shadow',\n          options: [{\n            label: 'Yes',\n            value: true\n          }, {\n            label: 'No',\n            value: false\n          }]\n        }\n      },\n      render: ({\n        title,\n        content,\n        imageUrl,\n        backgroundColor,\n        borderColor,\n        borderRadius,\n        padding,\n        shadow\n      }) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '1rem'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: backgroundColor || '#ffffff',\n            border: `1px solid ${borderColor || '#e0e0e0'}`,\n            borderRadius: borderRadius || '8px',\n            padding: padding || '1.5rem',\n            boxShadow: shadow ? '0 2px 4px rgba(0,0,0,0.1)' : 'none',\n            overflow: 'hidden'\n          },\n          children: [imageUrl && /*#__PURE__*/_jsxDEV(\"img\", {\n            src: imageUrl,\n            alt: title || 'Card image',\n            style: {\n              width: '100%',\n              height: '200px',\n              objectFit: 'cover',\n              marginBottom: '1rem',\n              borderRadius: '4px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 15\n          }, this), title && /*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              margin: '0 0 1rem 0',\n              fontSize: '1.25rem',\n              fontWeight: 'bold',\n              color: '#333'\n            },\n            children: title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 548,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            dangerouslySetInnerHTML: {\n              __html: content || '<p>Card content goes here...</p>'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 557,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 524,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 523,\n        columnNumber: 9\n      }, this)\n    },\n    Columns: {\n      label: 'Columns',\n      defaultProps: {\n        columns: 2,\n        gap: '2rem',\n        alignItems: 'stretch'\n      },\n      fields: {\n        columns: {\n          type: 'select',\n          label: 'Number of Columns',\n          options: [{\n            label: '2 Columns',\n            value: 2\n          }, {\n            label: '3 Columns',\n            value: 3\n          }, {\n            label: '4 Columns',\n            value: 4\n          }]\n        },\n        gap: {\n          type: 'text',\n          label: 'Gap Between Columns'\n        },\n        alignItems: {\n          type: 'select',\n          label: 'Vertical Alignment',\n          options: [{\n            label: 'Stretch',\n            value: 'stretch'\n          }, {\n            label: 'Top',\n            value: 'flex-start'\n          }, {\n            label: 'Center',\n            value: 'center'\n          }, {\n            label: 'Bottom',\n            value: 'flex-end'\n          }]\n        }\n      },\n      render: ({\n        columns,\n        gap,\n        alignItems,\n        children\n      }) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '1rem'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: `repeat(${columns || 2}, 1fr)`,\n            gap: gap || '2rem',\n            alignItems: alignItems || 'stretch'\n          },\n          children: children\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 594,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 593,\n        columnNumber: 9\n      }, this)\n    },\n    List: {\n      label: 'List',\n      defaultProps: {\n        items: ['List item 1', 'List item 2', 'List item 3'],\n        listType: 'ul',\n        color: '#333333',\n        fontSize: '16px'\n      },\n      fields: {\n        items: {\n          type: 'array',\n          label: 'List Items',\n          arrayFields: {\n            item: {\n              type: 'text'\n            }\n          }\n        },\n        listType: {\n          type: 'select',\n          label: 'List Type',\n          options: [{\n            label: 'Unordered (bullets)',\n            value: 'ul'\n          }, {\n            label: 'Ordered (numbers)',\n            value: 'ol'\n          }]\n        },\n        color: {\n          type: 'text',\n          label: 'Text Color'\n        },\n        fontSize: {\n          type: 'text',\n          label: 'Font Size'\n        }\n      },\n      render: ({\n        items,\n        listType,\n        color,\n        fontSize\n      }) => {\n        const ListTag = listType === 'ol' ? 'ol' : 'ul';\n        const itemsArray = Array.isArray(items) ? items : ['List item 1', 'List item 2', 'List item 3'];\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '1rem'\n          },\n          children: /*#__PURE__*/_jsxDEV(ListTag, {\n            style: {\n              color: color || '#333333',\n              fontSize: fontSize || '16px',\n              lineHeight: '1.6',\n              paddingLeft: '1.5rem'\n            },\n            children: itemsArray.map((item, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n              style: {\n                marginBottom: '0.5rem'\n              },\n              children: item\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 644,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 634,\n          columnNumber: 11\n        }, this);\n      }\n    }\n  }\n};\nconst PuckEditor = ({\n  className\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const [puckData, setPuckData] = useState({\n    content: [],\n    root: {\n      title: 'New Page',\n      metaDescription: ''\n    }\n  });\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState('');\n  const [isAdmin, setIsAdmin] = useState(false);\n  useEffect(() => {\n    checkAdminStatus();\n  }, []);\n\n  // Handle ResizeObserver errors more gracefully\n  useEffect(() => {\n    const cleanup = suppressResizeObserverErrors();\n\n    // Additional error handling for Puck-specific issues\n    const handlePuckError = event => {\n      // Suppress common Puck-related errors that don't affect functionality\n      if (event.message && (event.message.includes('ResizeObserver') || event.message.includes('Cannot read properties of null') || event.message.includes('Cannot read property') || event.message.includes('reading \\'offsetHeight\\'') || event.message.includes('reading \\'offsetWidth\\''))) {\n        event.preventDefault();\n        return false;\n      }\n      return true;\n    };\n    window.addEventListener('error', handlePuckError);\n    return () => {\n      cleanup();\n      window.removeEventListener('error', handlePuckError);\n    };\n  }, []);\n  const checkAdminStatus = async () => {\n    try {\n      setLoading(true);\n      setError('');\n\n      // Check if user is admin\n      const adminStatus = await authService.checkAdminStatus();\n      if (!adminStatus.is_admin) {\n        setError('You must be an administrator to use the visual editor.');\n        setLoading(false);\n        return;\n      }\n      setIsAdmin(true);\n    } catch (err) {\n      console.error('Error checking admin status:', err);\n      setError(err.message || 'Failed to verify admin status');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSave = async data => {\n    try {\n      setSaving(true);\n      setError('');\n\n      // For now, just save to localStorage as a demo\n      // In a real implementation, you would save to your backend\n      localStorage.setItem('puck-editor-data', JSON.stringify(data));\n\n      // Show success message\n      alert('Design saved successfully! (Demo mode - saved to localStorage)');\n\n      // Update the current data\n      setPuckData(data);\n    } catch (err) {\n      console.error('Error saving design:', err);\n      setError(err.message || 'Failed to save design');\n    } finally {\n      setSaving(false);\n    }\n  };\n  const handleCancel = () => {\n    navigate('/');\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      className: \"py-5 text-center\",\n      children: [/*#__PURE__*/_jsxDEV(Spinner, {\n        animation: \"border\",\n        role: \"status\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 758,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 757,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-3\",\n        children: \"Loading visual editor...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 760,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 756,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      className: \"py-5\",\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"danger\",\n        children: [/*#__PURE__*/_jsxDEV(Alert.Heading, {\n          children: \"Error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 769,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 770,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline-danger\",\n          onClick: () => navigate('/'),\n          children: \"Go Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 771,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 768,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 767,\n      columnNumber: 7\n    }, this);\n  }\n  if (!isAdmin) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      className: \"py-5\",\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"warning\",\n        children: [/*#__PURE__*/_jsxDEV(Alert.Heading, {\n          children: \"Access Denied\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 783,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"You must be an administrator to use the visual editor.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 784,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline-warning\",\n          onClick: () => navigate('/'),\n          children: \"Go Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 785,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 782,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 781,\n      columnNumber: 7\n    }, this);\n  }\n  if (!puckData) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      className: \"py-5\",\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"warning\",\n        children: [/*#__PURE__*/_jsxDEV(Alert.Heading, {\n          children: \"Editor Not Ready\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 797,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"The visual editor is not ready yet.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 798,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline-warning\",\n          onClick: () => navigate('/'),\n          children: \"Go Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 799,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 796,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 795,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"puck-editor-container\",\n    style: {\n      height: '100vh',\n      width: '100vw',\n      position: 'fixed',\n      top: 0,\n      left: 0,\n      zIndex: 9999\n    },\n    children: [/*#__PURE__*/_jsxDEV(Puck, {\n      config: puckConfig,\n      data: puckData,\n      onPublish: handleSave,\n      onChange: setPuckData,\n      headerTitle: \"Visual Editor\",\n      renderHeaderActions: () => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"puck-header-actions\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          size: \"sm\",\n          onClick: () => handleSave(puckData),\n          disabled: saving,\n          children: saving ? 'Saving...' : 'Save'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 817,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline-secondary\",\n          size: \"sm\",\n          onClick: handleCancel,\n          disabled: saving,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 825,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 816,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 809,\n      columnNumber: 7\n    }, this), saving && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundColor: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 10000\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: 'white',\n          padding: '2rem',\n          borderRadius: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Spinner, {\n          animation: \"border\",\n          className: \"me-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 852,\n          columnNumber: 13\n        }, this), \"Saving design...\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 851,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 837,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 808,\n    columnNumber: 5\n  }, this);\n};\n_s(PuckEditor, \"HdLA+tDzUoInR5y36cM0U3SszW0=\", false, function () {\n  return [useNavigate];\n});\n_c = PuckEditor;\nexport default PuckEditor;\nvar _c;\n$RefreshReg$(_c, \"PuckEditor\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON>", "useNavigate", "Container", "<PERSON><PERSON>", "Spinner", "<PERSON><PERSON>", "authService", "suppressResizeObserverErrors", "jsxDEV", "_jsxDEV", "puckConfig", "components", "Hero", "label", "defaultProps", "title", "subtitle", "textAlign", "minHeight", "backgroundType", "backgroundColor", "backgroundGradient", "textColor", "fields", "type", "options", "value", "backgroundImage", "render", "backgroundStyle", "background", "backgroundSize", "backgroundPosition", "backgroundRepeat", "style", "display", "flexDirection", "justifyContent", "alignItems", "padding", "color", "position", "children", "zIndex", "max<PERSON><PERSON><PERSON>", "fontSize", "marginBottom", "fontWeight", "lineHeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "opacity", "Text", "content", "marginTop", "className", "dangerouslySetInnerHTML", "__html", "Heading", "text", "level", "HeadingTag", "fontSizes", "h1", "h2", "h3", "h4", "h5", "h6", "createElement", "href", "variant", "size", "fullWidth", "target", "colors", "primary", "secondary", "success", "danger", "warning", "info", "light", "dark", "textColors", "rel", "undefined", "width", "textDecoration", "borderRadius", "transition", "border", "cursor", "onMouseOver", "e", "currentTarget", "transform", "onMouseOut", "Image", "src", "alt", "height", "objectFit", "maxWid<PERSON>", "sm", "md", "lg", "xl", "fluid", "margin", "Spacer", "Divider", "thickness", "borderTop", "Card", "imageUrl", "borderColor", "shadow", "boxShadow", "overflow", "Columns", "columns", "gap", "gridTemplateColumns", "List", "items", "listType", "arrayFields", "item", "ListTag", "itemsArray", "Array", "isArray", "paddingLeft", "map", "index", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_s", "navigate", "puck<PERSON><PERSON>", "setPuckData", "root", "metaDescription", "loading", "setLoading", "saving", "setSaving", "error", "setError", "isAdmin", "setIsAdmin", "checkAdminStatus", "cleanup", "handlePuckError", "event", "message", "includes", "preventDefault", "window", "addEventListener", "removeEventListener", "adminStatus", "is_admin", "err", "console", "handleSave", "data", "localStorage", "setItem", "JSON", "stringify", "alert", "handleCancel", "animation", "role", "onClick", "top", "left", "config", "onPublish", "onChange", "headerTitle", "renderHeaderActions", "disabled", "right", "bottom", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/frontend/src/components/puck/PuckEditor.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Puck } from '@measured/puck';\nimport '@measured/puck/puck.css';\nimport './PuckEditor.css';\nimport { useNavigate } from 'react-router-dom';\nimport { Container, <PERSON><PERSON>, Spin<PERSON>, <PERSON><PERSON> } from 'react-bootstrap';\nimport authService from '../../services/authService';\nimport { suppressResizeObserverErrors } from '../../utils/errorHandlers';\n\n// Comprehensive Puck configuration with all essential components\nconst puckConfig = {\n  components: {\n    // Layout Components\n    Hero: {\n      label: 'Hero Section',\n      defaultProps: {\n        title: 'Hero Title',\n        subtitle: 'Hero subtitle text',\n        textAlign: 'center',\n        minHeight: '400px',\n        backgroundType: 'gradient',\n        backgroundColor: '#667eea',\n        backgroundGradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        textColor: '#ffffff',\n      },\n      fields: {\n        title: { type: 'text' as const, label: 'Title' },\n        subtitle: { type: 'textarea' as const, label: 'Subtitle' },\n        textAlign: {\n          type: 'select' as const,\n          label: 'Text Alignment',\n          options: [\n            { label: 'Left', value: 'left' },\n            { label: 'Center', value: 'center' },\n            { label: 'Right', value: 'right' },\n          ],\n        },\n        minHeight: { type: 'text' as const, label: 'Min Height (px)' },\n        backgroundType: {\n          type: 'select' as const,\n          label: 'Background Type',\n          options: [\n            { label: 'Solid Color', value: 'solid' },\n            { label: 'Gradient', value: 'gradient' },\n            { label: 'Image', value: 'image' },\n          ],\n        },\n        backgroundColor: { type: 'text' as const, label: 'Background Color' },\n        backgroundGradient: { type: 'text' as const, label: 'Background Gradient' },\n        backgroundImage: { type: 'text' as const, label: 'Background Image URL' },\n        textColor: { type: 'text' as const, label: 'Text Color' },\n      },\n      render: ({ title, subtitle, textAlign, minHeight, backgroundType, backgroundColor, backgroundGradient, backgroundImage, textColor }: any) => {\n        let backgroundStyle = {};\n\n        switch (backgroundType) {\n          case 'solid':\n            backgroundStyle = { backgroundColor: backgroundColor || '#667eea' };\n            break;\n          case 'gradient':\n            backgroundStyle = { background: backgroundGradient || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' };\n            break;\n          case 'image':\n            backgroundStyle = {\n              backgroundImage: `url(${backgroundImage})`,\n              backgroundSize: 'cover',\n              backgroundPosition: 'center',\n              backgroundRepeat: 'no-repeat'\n            };\n            break;\n          default:\n            backgroundStyle = { background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' };\n        }\n\n        return (\n          <section\n            style={{\n              minHeight: minHeight || '400px',\n              display: 'flex',\n              flexDirection: 'column',\n              justifyContent: 'center',\n              alignItems: textAlign === 'center' ? 'center' : textAlign === 'right' ? 'flex-end' : 'flex-start',\n              textAlign: textAlign || 'center',\n              padding: '4rem 2rem',\n              color: textColor || 'white',\n              position: 'relative',\n              ...backgroundStyle,\n            }}\n          >\n            <div style={{ position: 'relative', zIndex: 1, maxWidth: '800px' }}>\n              <h1 style={{\n                fontSize: 'clamp(2rem, 5vw, 3.5rem)',\n                marginBottom: '1rem',\n                fontWeight: 'bold',\n                lineHeight: 1.2,\n              }}>\n                {title || 'Hero Title'}\n              </h1>\n              <p style={{\n                fontSize: 'clamp(1rem, 2.5vw, 1.25rem)',\n                opacity: 0.9,\n                lineHeight: 1.6,\n                marginBottom: '2rem',\n              }}>\n                {subtitle || 'Hero subtitle text'}\n              </p>\n            </div>\n          </section>\n        );\n      },\n    },\n    // Content Components\n    Text: {\n      label: 'Text Block',\n      defaultProps: {\n        content: '<p>Enter your text content here...</p>',\n        textAlign: 'left',\n        fontSize: '16px',\n        color: '#333333',\n        lineHeight: '1.6',\n        marginTop: '0px',\n        marginBottom: '20px',\n      },\n      fields: {\n        content: { type: 'textarea' as const, label: 'Content (HTML)' },\n        textAlign: {\n          type: 'select' as const,\n          label: 'Text Alignment',\n          options: [\n            { label: 'Left', value: 'left' },\n            { label: 'Center', value: 'center' },\n            { label: 'Right', value: 'right' },\n            { label: 'Justify', value: 'justify' },\n          ],\n        },\n        fontSize: { type: 'text' as const, label: 'Font Size' },\n        color: { type: 'text' as const, label: 'Text Color' },\n        lineHeight: { type: 'text' as const, label: 'Line Height' },\n        marginTop: { type: 'text' as const, label: 'Margin Top' },\n        marginBottom: { type: 'text' as const, label: 'Margin Bottom' },\n      },\n      render: ({ content, textAlign, fontSize, color, lineHeight, marginTop, marginBottom }: any) => (\n        <div\n          className=\"text-component\"\n          style={{\n            textAlign: textAlign || 'left',\n            fontSize: fontSize || '16px',\n            color: color || '#333333',\n            lineHeight: lineHeight || '1.6',\n            marginTop: marginTop || '0px',\n            marginBottom: marginBottom || '20px',\n            padding: '0 1rem',\n          }}\n        >\n          <div dangerouslySetInnerHTML={{ __html: content || '<p>Enter your text content here...</p>' }} />\n        </div>\n      ),\n    },\n\n    Heading: {\n      label: 'Heading',\n      defaultProps: {\n        text: 'Heading Text',\n        level: 'h2',\n        textAlign: 'left',\n        color: '#333333',\n        marginTop: '0px',\n        marginBottom: '20px',\n      },\n      fields: {\n        text: { type: 'text' as const, label: 'Heading Text' },\n        level: {\n          type: 'select' as const,\n          label: 'Heading Level',\n          options: [\n            { label: 'H1', value: 'h1' },\n            { label: 'H2', value: 'h2' },\n            { label: 'H3', value: 'h3' },\n            { label: 'H4', value: 'h4' },\n            { label: 'H5', value: 'h5' },\n            { label: 'H6', value: 'h6' },\n          ],\n        },\n        textAlign: {\n          type: 'select' as const,\n          label: 'Text Alignment',\n          options: [\n            { label: 'Left', value: 'left' },\n            { label: 'Center', value: 'center' },\n            { label: 'Right', value: 'right' },\n          ],\n        },\n        color: { type: 'text' as const, label: 'Text Color' },\n        marginTop: { type: 'text' as const, label: 'Margin Top' },\n        marginBottom: { type: 'text' as const, label: 'Margin Bottom' },\n      },\n      render: ({ text, level, textAlign, color, marginTop, marginBottom }: any) => {\n        const HeadingTag = level || 'h2';\n        const fontSizes: { [key: string]: string } = {\n          h1: '2.5rem',\n          h2: '2rem',\n          h3: '1.75rem',\n          h4: '1.5rem',\n          h5: '1.25rem',\n          h6: '1rem',\n        };\n        const fontSize = fontSizes[level] || '2rem';\n\n        return React.createElement(\n          HeadingTag,\n          {\n            style: {\n              textAlign: textAlign || 'left',\n              color: color || '#333333',\n              fontSize,\n              fontWeight: 'bold',\n              marginTop: marginTop || '0px',\n              marginBottom: marginBottom || '20px',\n              padding: '0 1rem',\n              lineHeight: 1.2,\n            }\n          },\n          text || 'Heading Text'\n        );\n      },\n    },\n    Button: {\n      label: 'Button',\n      defaultProps: {\n        text: 'Button Text',\n        href: '#',\n        variant: 'primary',\n        size: 'md',\n        textAlign: 'center',\n        fullWidth: false,\n        target: '_self',\n      },\n      fields: {\n        text: { type: 'text' as const, label: 'Button Text' },\n        href: { type: 'text' as const, label: 'Link URL' },\n        variant: {\n          type: 'select' as const,\n          label: 'Button Style',\n          options: [\n            { label: 'Primary', value: 'primary' },\n            { label: 'Secondary', value: 'secondary' },\n            { label: 'Success', value: 'success' },\n            { label: 'Danger', value: 'danger' },\n            { label: 'Warning', value: 'warning' },\n            { label: 'Info', value: 'info' },\n            { label: 'Light', value: 'light' },\n            { label: 'Dark', value: 'dark' },\n          ],\n        },\n        size: {\n          type: 'select' as const,\n          label: 'Button Size',\n          options: [\n            { label: 'Small', value: 'sm' },\n            { label: 'Medium', value: 'md' },\n            { label: 'Large', value: 'lg' },\n          ],\n        },\n        textAlign: {\n          type: 'select' as const,\n          label: 'Alignment',\n          options: [\n            { label: 'Left', value: 'left' },\n            { label: 'Center', value: 'center' },\n            { label: 'Right', value: 'right' },\n          ],\n        },\n        fullWidth: { type: 'radio' as const, label: 'Full Width', options: [{ label: 'Yes', value: true }, { label: 'No', value: false }] },\n        target: {\n          type: 'select' as const,\n          label: 'Link Target',\n          options: [\n            { label: 'Same Window', value: '_self' },\n            { label: 'New Window', value: '_blank' },\n          ],\n        },\n      },\n      render: ({ text, href, variant, size, textAlign, fullWidth, target }: any) => {\n        const colors: { [key: string]: string } = {\n          primary: '#007bff',\n          secondary: '#6c757d',\n          success: '#28a745',\n          danger: '#dc3545',\n          warning: '#ffc107',\n          info: '#17a2b8',\n          light: '#f8f9fa',\n          dark: '#343a40',\n        };\n\n        const textColors: { [key: string]: string } = {\n          primary: 'white',\n          secondary: 'white',\n          success: 'white',\n          danger: 'white',\n          warning: '#212529',\n          info: 'white',\n          light: '#212529',\n          dark: 'white',\n        };\n\n        const padding = size === 'sm' ? '0.5rem 1rem' : size === 'lg' ? '1rem 2rem' : '0.75rem 1.5rem';\n        const fontSize = size === 'sm' ? '0.875rem' : size === 'lg' ? '1.125rem' : '1rem';\n\n        return (\n          <div style={{ padding: '1rem', textAlign: textAlign || 'center' }}>\n            <a\n              href={href || '#'}\n              target={target || '_self'}\n              rel={target === '_blank' ? 'noopener noreferrer' : undefined}\n              style={{\n                display: fullWidth ? 'block' : 'inline-block',\n                width: fullWidth ? '100%' : 'auto',\n                padding,\n                backgroundColor: colors[variant] || colors.primary,\n                color: textColors[variant] || 'white',\n                textDecoration: 'none',\n                borderRadius: '0.375rem',\n                fontWeight: '500',\n                fontSize,\n                textAlign: 'center',\n                transition: 'all 0.2s ease-in-out',\n                border: 'none',\n                cursor: 'pointer',\n              }}\n              onMouseOver={(e) => {\n                e.currentTarget.style.opacity = '0.9';\n                e.currentTarget.style.transform = 'translateY(-1px)';\n              }}\n              onMouseOut={(e) => {\n                e.currentTarget.style.opacity = '1';\n                e.currentTarget.style.transform = 'translateY(0)';\n              }}\n            >\n              {text || 'Button Text'}\n            </a>\n          </div>\n        );\n      },\n    },\n    Image: {\n      label: 'Image',\n      defaultProps: {\n        src: 'https://via.placeholder.com/600x400',\n        alt: 'Image description',\n        width: '100%',\n        height: 'auto',\n        objectFit: 'cover',\n        borderRadius: '0px',\n        marginTop: '0px',\n        marginBottom: '20px',\n      },\n      fields: {\n        src: { type: 'text' as const, label: 'Image URL' },\n        alt: { type: 'text' as const, label: 'Alt Text' },\n        width: { type: 'text' as const, label: 'Width' },\n        height: { type: 'text' as const, label: 'Height' },\n        objectFit: {\n          type: 'select' as const,\n          label: 'Object Fit',\n          options: [\n            { label: 'Cover', value: 'cover' },\n            { label: 'Contain', value: 'contain' },\n            { label: 'Fill', value: 'fill' },\n            { label: 'None', value: 'none' },\n            { label: 'Scale Down', value: 'scale-down' },\n          ],\n        },\n        borderRadius: { type: 'text' as const, label: 'Border Radius' },\n        marginTop: { type: 'text' as const, label: 'Margin Top' },\n        marginBottom: { type: 'text' as const, label: 'Margin Bottom' },\n      },\n      render: ({ src, alt, width, height, objectFit, borderRadius, marginTop, marginBottom }: any) => (\n        <div style={{ padding: '1rem', marginTop: marginTop || '0px', marginBottom: marginBottom || '20px' }}>\n          <img\n            src={src || 'https://via.placeholder.com/600x400'}\n            alt={alt || 'Image description'}\n            style={{\n              width: width || '100%',\n              height: height || 'auto',\n              objectFit: objectFit || 'cover',\n              borderRadius: borderRadius || '0px',\n              display: 'block',\n              maxWidth: '100%',\n            }}\n          />\n        </div>\n      ),\n    },\n\n    Container: {\n      label: 'Container',\n      defaultProps: {\n        maxWidth: 'lg',\n        padding: '2rem',\n        backgroundColor: 'transparent',\n        borderRadius: '0px',\n        marginTop: '0px',\n        marginBottom: '0px',\n      },\n      fields: {\n        maxWidth: {\n          type: 'select' as const,\n          label: 'Max Width',\n          options: [\n            { label: 'Small (540px)', value: 'sm' },\n            { label: 'Medium (720px)', value: 'md' },\n            { label: 'Large (960px)', value: 'lg' },\n            { label: 'Extra Large (1140px)', value: 'xl' },\n            { label: 'Full Width', value: 'fluid' },\n          ],\n        },\n        padding: { type: 'text' as const, label: 'Padding' },\n        backgroundColor: { type: 'text' as const, label: 'Background Color' },\n        borderRadius: { type: 'text' as const, label: 'Border Radius' },\n        marginTop: { type: 'text' as const, label: 'Margin Top' },\n        marginBottom: { type: 'text' as const, label: 'Margin Bottom' },\n      },\n      render: ({ maxWidth, padding, backgroundColor, borderRadius, marginTop, marginBottom, children }: any) => {\n        const maxWidths: { [key: string]: string } = {\n          sm: '540px',\n          md: '720px',\n          lg: '960px',\n          xl: '1140px',\n          fluid: '100%',\n        };\n\n        return (\n          <div\n            style={{\n              maxWidth: maxWidths[maxWidth] || '960px',\n              margin: '0 auto',\n              padding: padding || '2rem',\n              backgroundColor: backgroundColor || 'transparent',\n              borderRadius: borderRadius || '0px',\n              marginTop: marginTop || '0px',\n              marginBottom: marginBottom || '0px',\n            }}\n          >\n            {children}\n          </div>\n        );\n      },\n    },\n\n    Spacer: {\n      label: 'Spacer',\n      defaultProps: {\n        height: '40px',\n      },\n      fields: {\n        height: { type: 'text' as const, label: 'Height' },\n      },\n      render: ({ height }: any) => (\n        <div style={{ height: height || '40px', width: '100%' }} />\n      ),\n    },\n\n    Divider: {\n      label: 'Divider',\n      defaultProps: {\n        color: '#e0e0e0',\n        thickness: '1px',\n        style: 'solid',\n        marginTop: '20px',\n        marginBottom: '20px',\n      },\n      fields: {\n        color: { type: 'text' as const, label: 'Color' },\n        thickness: { type: 'text' as const, label: 'Thickness' },\n        style: {\n          type: 'select' as const,\n          label: 'Style',\n          options: [\n            { label: 'Solid', value: 'solid' },\n            { label: 'Dashed', value: 'dashed' },\n            { label: 'Dotted', value: 'dotted' },\n          ],\n        },\n        marginTop: { type: 'text' as const, label: 'Margin Top' },\n        marginBottom: { type: 'text' as const, label: 'Margin Bottom' },\n      },\n      render: ({ color, thickness, style, marginTop, marginBottom }: any) => (\n        <div style={{ padding: '0 1rem', marginTop: marginTop || '20px', marginBottom: marginBottom || '20px' }}>\n          <hr\n            style={{\n              border: 'none',\n              borderTop: `${thickness || '1px'} ${style || 'solid'} ${color || '#e0e0e0'}`,\n              margin: 0,\n            }}\n          />\n        </div>\n      ),\n    },\n\n    Card: {\n      label: 'Card',\n      defaultProps: {\n        title: 'Card Title',\n        content: '<p>Card content goes here...</p>',\n        imageUrl: '',\n        backgroundColor: '#ffffff',\n        borderColor: '#e0e0e0',\n        borderRadius: '8px',\n        padding: '1.5rem',\n        shadow: true,\n      },\n      fields: {\n        title: { type: 'text' as const, label: 'Card Title' },\n        content: { type: 'textarea' as const, label: 'Card Content (HTML)' },\n        imageUrl: { type: 'text' as const, label: 'Image URL (optional)' },\n        backgroundColor: { type: 'text' as const, label: 'Background Color' },\n        borderColor: { type: 'text' as const, label: 'Border Color' },\n        borderRadius: { type: 'text' as const, label: 'Border Radius' },\n        padding: { type: 'text' as const, label: 'Padding' },\n        shadow: { type: 'radio' as const, label: 'Drop Shadow', options: [{ label: 'Yes', value: true }, { label: 'No', value: false }] },\n      },\n      render: ({ title, content, imageUrl, backgroundColor, borderColor, borderRadius, padding, shadow }: any) => (\n        <div style={{ padding: '1rem' }}>\n          <div\n            style={{\n              backgroundColor: backgroundColor || '#ffffff',\n              border: `1px solid ${borderColor || '#e0e0e0'}`,\n              borderRadius: borderRadius || '8px',\n              padding: padding || '1.5rem',\n              boxShadow: shadow ? '0 2px 4px rgba(0,0,0,0.1)' : 'none',\n              overflow: 'hidden',\n            }}\n          >\n            {imageUrl && (\n              <img\n                src={imageUrl}\n                alt={title || 'Card image'}\n                style={{\n                  width: '100%',\n                  height: '200px',\n                  objectFit: 'cover',\n                  marginBottom: '1rem',\n                  borderRadius: '4px',\n                }}\n              />\n            )}\n            {title && (\n              <h3 style={{\n                margin: '0 0 1rem 0',\n                fontSize: '1.25rem',\n                fontWeight: 'bold',\n                color: '#333',\n              }}>\n                {title}\n              </h3>\n            )}\n            <div dangerouslySetInnerHTML={{ __html: content || '<p>Card content goes here...</p>' }} />\n          </div>\n        </div>\n      ),\n    },\n\n    Columns: {\n      label: 'Columns',\n      defaultProps: {\n        columns: 2,\n        gap: '2rem',\n        alignItems: 'stretch',\n      },\n      fields: {\n        columns: {\n          type: 'select' as const,\n          label: 'Number of Columns',\n          options: [\n            { label: '2 Columns', value: 2 },\n            { label: '3 Columns', value: 3 },\n            { label: '4 Columns', value: 4 },\n          ],\n        },\n        gap: { type: 'text' as const, label: 'Gap Between Columns' },\n        alignItems: {\n          type: 'select' as const,\n          label: 'Vertical Alignment',\n          options: [\n            { label: 'Stretch', value: 'stretch' },\n            { label: 'Top', value: 'flex-start' },\n            { label: 'Center', value: 'center' },\n            { label: 'Bottom', value: 'flex-end' },\n          ],\n        },\n      },\n      render: ({ columns, gap, alignItems, children }: any) => (\n        <div style={{ padding: '1rem' }}>\n          <div\n            style={{\n              display: 'grid',\n              gridTemplateColumns: `repeat(${columns || 2}, 1fr)`,\n              gap: gap || '2rem',\n              alignItems: alignItems || 'stretch',\n            }}\n          >\n            {children}\n          </div>\n        </div>\n      ),\n    },\n\n    List: {\n      label: 'List',\n      defaultProps: {\n        items: ['List item 1', 'List item 2', 'List item 3'],\n        listType: 'ul',\n        color: '#333333',\n        fontSize: '16px',\n      },\n      fields: {\n        items: { type: 'array' as const, label: 'List Items', arrayFields: { item: { type: 'text' as const } } },\n        listType: {\n          type: 'select' as const,\n          label: 'List Type',\n          options: [\n            { label: 'Unordered (bullets)', value: 'ul' },\n            { label: 'Ordered (numbers)', value: 'ol' },\n          ],\n        },\n        color: { type: 'text' as const, label: 'Text Color' },\n        fontSize: { type: 'text' as const, label: 'Font Size' },\n      },\n      render: ({ items, listType, color, fontSize }: any) => {\n        const ListTag = listType === 'ol' ? 'ol' : 'ul';\n        const itemsArray = Array.isArray(items) ? items : ['List item 1', 'List item 2', 'List item 3'];\n\n        return (\n          <div style={{ padding: '1rem' }}>\n            <ListTag\n              style={{\n                color: color || '#333333',\n                fontSize: fontSize || '16px',\n                lineHeight: '1.6',\n                paddingLeft: '1.5rem',\n              }}\n            >\n              {itemsArray.map((item: string, index: number) => (\n                <li key={index} style={{ marginBottom: '0.5rem' }}>\n                  {item}\n                </li>\n              ))}\n            </ListTag>\n          </div>\n        );\n      },\n    },\n  },\n};\n\ninterface PuckEditorProps {\n  className?: string;\n}\n\nconst PuckEditor: React.FC<PuckEditorProps> = ({ className }) => {\n  const navigate = useNavigate();\n\n  const [puckData, setPuckData] = useState<any>({\n    content: [],\n    root: {\n      title: 'New Page',\n      metaDescription: '',\n    },\n  });\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState<string>('');\n  const [isAdmin, setIsAdmin] = useState(false);\n\n  useEffect(() => {\n    checkAdminStatus();\n  }, []);\n\n  // Handle ResizeObserver errors more gracefully\n  useEffect(() => {\n    const cleanup = suppressResizeObserverErrors();\n\n    // Additional error handling for Puck-specific issues\n    const handlePuckError = (event: ErrorEvent) => {\n      // Suppress common Puck-related errors that don't affect functionality\n      if (event.message && (\n        event.message.includes('ResizeObserver') ||\n        event.message.includes('Cannot read properties of null') ||\n        event.message.includes('Cannot read property') ||\n        event.message.includes('reading \\'offsetHeight\\'') ||\n        event.message.includes('reading \\'offsetWidth\\'')\n      )) {\n        event.preventDefault();\n        return false;\n      }\n      return true;\n    };\n\n    window.addEventListener('error', handlePuckError);\n\n    return () => {\n      cleanup();\n      window.removeEventListener('error', handlePuckError);\n    };\n  }, []);\n\n  const checkAdminStatus = async () => {\n    try {\n      setLoading(true);\n      setError('');\n\n      // Check if user is admin\n      const adminStatus = await authService.checkAdminStatus();\n      if (!adminStatus.is_admin) {\n        setError('You must be an administrator to use the visual editor.');\n        setLoading(false);\n        return;\n      }\n      setIsAdmin(true);\n    } catch (err: any) {\n      console.error('Error checking admin status:', err);\n      setError(err.message || 'Failed to verify admin status');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSave = async (data: any) => {\n    try {\n      setSaving(true);\n      setError('');\n\n      // For now, just save to localStorage as a demo\n      // In a real implementation, you would save to your backend\n      localStorage.setItem('puck-editor-data', JSON.stringify(data));\n\n      // Show success message\n      alert('Design saved successfully! (Demo mode - saved to localStorage)');\n\n      // Update the current data\n      setPuckData(data);\n    } catch (err: any) {\n      console.error('Error saving design:', err);\n      setError(err.message || 'Failed to save design');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const handleCancel = () => {\n    navigate('/');\n  };\n\n  if (loading) {\n    return (\n      <Container className=\"py-5 text-center\">\n        <Spinner animation=\"border\" role=\"status\">\n          <span className=\"visually-hidden\">Loading...</span>\n        </Spinner>\n        <p className=\"mt-3\">Loading visual editor...</p>\n      </Container>\n    );\n  }\n\n  if (error) {\n    return (\n      <Container className=\"py-5\">\n        <Alert variant=\"danger\">\n          <Alert.Heading>Error</Alert.Heading>\n          <p>{error}</p>\n          <Button variant=\"outline-danger\" onClick={() => navigate('/')}>\n            Go Home\n          </Button>\n        </Alert>\n      </Container>\n    );\n  }\n\n  if (!isAdmin) {\n    return (\n      <Container className=\"py-5\">\n        <Alert variant=\"warning\">\n          <Alert.Heading>Access Denied</Alert.Heading>\n          <p>You must be an administrator to use the visual editor.</p>\n          <Button variant=\"outline-warning\" onClick={() => navigate('/')}>\n            Go Home\n          </Button>\n        </Alert>\n      </Container>\n    );\n  }\n\n  if (!puckData) {\n    return (\n      <Container className=\"py-5\">\n        <Alert variant=\"warning\">\n          <Alert.Heading>Editor Not Ready</Alert.Heading>\n          <p>The visual editor is not ready yet.</p>\n          <Button variant=\"outline-warning\" onClick={() => navigate('/')}>\n            Go Home\n          </Button>\n        </Alert>\n      </Container>\n    );\n  }\n\n  return (\n    <div className=\"puck-editor-container\" style={{ height: '100vh', width: '100vw', position: 'fixed', top: 0, left: 0, zIndex: 9999 }}>\n      <Puck\n        config={puckConfig}\n        data={puckData}\n        onPublish={handleSave}\n        onChange={setPuckData}\n        headerTitle=\"Visual Editor\"\n        renderHeaderActions={() => (\n          <div className=\"puck-header-actions\">\n            <Button\n              variant=\"primary\"\n              size=\"sm\"\n              onClick={() => handleSave(puckData)}\n              disabled={saving}\n            >\n              {saving ? 'Saving...' : 'Save'}\n            </Button>\n            <Button\n              variant=\"outline-secondary\"\n              size=\"sm\"\n              onClick={handleCancel}\n              disabled={saving}\n            >\n              Cancel\n            </Button>\n          </div>\n        )}\n      />\n      {saving && (\n        <div\n          style={{\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            backgroundColor: 'rgba(0, 0, 0, 0.5)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            zIndex: 10000,\n          }}\n        >\n          <div style={{ backgroundColor: 'white', padding: '2rem', borderRadius: '0.5rem' }}>\n            <Spinner animation=\"border\" className=\"me-2\" />\n            Saving design...\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default PuckEditor;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,gBAAgB;AACrC,OAAO,yBAAyB;AAChC,OAAO,kBAAkB;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,SAAS,EAAEC,KAAK,EAAEC,OAAO,EAAEC,MAAM,QAAQ,iBAAiB;AACnE,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,4BAA4B,QAAQ,2BAA2B;;AAExE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,UAAU,GAAG;EACjBC,UAAU,EAAE;IACV;IACAC,IAAI,EAAE;MACJC,KAAK,EAAE,cAAc;MACrBC,YAAY,EAAE;QACZC,KAAK,EAAE,YAAY;QACnBC,QAAQ,EAAE,oBAAoB;QAC9BC,SAAS,EAAE,QAAQ;QACnBC,SAAS,EAAE,OAAO;QAClBC,cAAc,EAAE,UAAU;QAC1BC,eAAe,EAAE,SAAS;QAC1BC,kBAAkB,EAAE,mDAAmD;QACvEC,SAAS,EAAE;MACb,CAAC;MACDC,MAAM,EAAE;QACNR,KAAK,EAAE;UAAES,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAQ,CAAC;QAChDG,QAAQ,EAAE;UAAEQ,IAAI,EAAE,UAAmB;UAAEX,KAAK,EAAE;QAAW,CAAC;QAC1DI,SAAS,EAAE;UACTO,IAAI,EAAE,QAAiB;UACvBX,KAAK,EAAE,gBAAgB;UACvBY,OAAO,EAAE,CACP;YAAEZ,KAAK,EAAE,MAAM;YAAEa,KAAK,EAAE;UAAO,CAAC,EAChC;YAAEb,KAAK,EAAE,QAAQ;YAAEa,KAAK,EAAE;UAAS,CAAC,EACpC;YAAEb,KAAK,EAAE,OAAO;YAAEa,KAAK,EAAE;UAAQ,CAAC;QAEtC,CAAC;QACDR,SAAS,EAAE;UAAEM,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAkB,CAAC;QAC9DM,cAAc,EAAE;UACdK,IAAI,EAAE,QAAiB;UACvBX,KAAK,EAAE,iBAAiB;UACxBY,OAAO,EAAE,CACP;YAAEZ,KAAK,EAAE,aAAa;YAAEa,KAAK,EAAE;UAAQ,CAAC,EACxC;YAAEb,KAAK,EAAE,UAAU;YAAEa,KAAK,EAAE;UAAW,CAAC,EACxC;YAAEb,KAAK,EAAE,OAAO;YAAEa,KAAK,EAAE;UAAQ,CAAC;QAEtC,CAAC;QACDN,eAAe,EAAE;UAAEI,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAmB,CAAC;QACrEQ,kBAAkB,EAAE;UAAEG,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAsB,CAAC;QAC3Ec,eAAe,EAAE;UAAEH,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAuB,CAAC;QACzES,SAAS,EAAE;UAAEE,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAa;MAC1D,CAAC;MACDe,MAAM,EAAEA,CAAC;QAAEb,KAAK;QAAEC,QAAQ;QAAEC,SAAS;QAAEC,SAAS;QAAEC,cAAc;QAAEC,eAAe;QAAEC,kBAAkB;QAAEM,eAAe;QAAEL;MAAe,CAAC,KAAK;QAC3I,IAAIO,eAAe,GAAG,CAAC,CAAC;QAExB,QAAQV,cAAc;UACpB,KAAK,OAAO;YACVU,eAAe,GAAG;cAAET,eAAe,EAAEA,eAAe,IAAI;YAAU,CAAC;YACnE;UACF,KAAK,UAAU;YACbS,eAAe,GAAG;cAAEC,UAAU,EAAET,kBAAkB,IAAI;YAAoD,CAAC;YAC3G;UACF,KAAK,OAAO;YACVQ,eAAe,GAAG;cAChBF,eAAe,EAAE,OAAOA,eAAe,GAAG;cAC1CI,cAAc,EAAE,OAAO;cACvBC,kBAAkB,EAAE,QAAQ;cAC5BC,gBAAgB,EAAE;YACpB,CAAC;YACD;UACF;YACEJ,eAAe,GAAG;cAAEC,UAAU,EAAE;YAAoD,CAAC;QACzF;QAEA,oBACErB,OAAA;UACEyB,KAAK,EAAE;YACLhB,SAAS,EAAEA,SAAS,IAAI,OAAO;YAC/BiB,OAAO,EAAE,MAAM;YACfC,aAAa,EAAE,QAAQ;YACvBC,cAAc,EAAE,QAAQ;YACxBC,UAAU,EAAErB,SAAS,KAAK,QAAQ,GAAG,QAAQ,GAAGA,SAAS,KAAK,OAAO,GAAG,UAAU,GAAG,YAAY;YACjGA,SAAS,EAAEA,SAAS,IAAI,QAAQ;YAChCsB,OAAO,EAAE,WAAW;YACpBC,KAAK,EAAElB,SAAS,IAAI,OAAO;YAC3BmB,QAAQ,EAAE,UAAU;YACpB,GAAGZ;UACL,CAAE;UAAAa,QAAA,eAEFjC,OAAA;YAAKyB,KAAK,EAAE;cAAEO,QAAQ,EAAE,UAAU;cAAEE,MAAM,EAAE,CAAC;cAAEC,QAAQ,EAAE;YAAQ,CAAE;YAAAF,QAAA,gBACjEjC,OAAA;cAAIyB,KAAK,EAAE;gBACTW,QAAQ,EAAE,0BAA0B;gBACpCC,YAAY,EAAE,MAAM;gBACpBC,UAAU,EAAE,MAAM;gBAClBC,UAAU,EAAE;cACd,CAAE;cAAAN,QAAA,EACC3B,KAAK,IAAI;YAAY;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eACL3C,OAAA;cAAGyB,KAAK,EAAE;gBACRW,QAAQ,EAAE,6BAA6B;gBACvCQ,OAAO,EAAE,GAAG;gBACZL,UAAU,EAAE,GAAG;gBACfF,YAAY,EAAE;cAChB,CAAE;cAAAJ,QAAA,EACC1B,QAAQ,IAAI;YAAoB;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAEd;IACF,CAAC;IACD;IACAE,IAAI,EAAE;MACJzC,KAAK,EAAE,YAAY;MACnBC,YAAY,EAAE;QACZyC,OAAO,EAAE,wCAAwC;QACjDtC,SAAS,EAAE,MAAM;QACjB4B,QAAQ,EAAE,MAAM;QAChBL,KAAK,EAAE,SAAS;QAChBQ,UAAU,EAAE,KAAK;QACjBQ,SAAS,EAAE,KAAK;QAChBV,YAAY,EAAE;MAChB,CAAC;MACDvB,MAAM,EAAE;QACNgC,OAAO,EAAE;UAAE/B,IAAI,EAAE,UAAmB;UAAEX,KAAK,EAAE;QAAiB,CAAC;QAC/DI,SAAS,EAAE;UACTO,IAAI,EAAE,QAAiB;UACvBX,KAAK,EAAE,gBAAgB;UACvBY,OAAO,EAAE,CACP;YAAEZ,KAAK,EAAE,MAAM;YAAEa,KAAK,EAAE;UAAO,CAAC,EAChC;YAAEb,KAAK,EAAE,QAAQ;YAAEa,KAAK,EAAE;UAAS,CAAC,EACpC;YAAEb,KAAK,EAAE,OAAO;YAAEa,KAAK,EAAE;UAAQ,CAAC,EAClC;YAAEb,KAAK,EAAE,SAAS;YAAEa,KAAK,EAAE;UAAU,CAAC;QAE1C,CAAC;QACDmB,QAAQ,EAAE;UAAErB,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAY,CAAC;QACvD2B,KAAK,EAAE;UAAEhB,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAa,CAAC;QACrDmC,UAAU,EAAE;UAAExB,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAc,CAAC;QAC3D2C,SAAS,EAAE;UAAEhC,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAa,CAAC;QACzDiC,YAAY,EAAE;UAAEtB,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAgB;MAChE,CAAC;MACDe,MAAM,EAAEA,CAAC;QAAE2B,OAAO;QAAEtC,SAAS;QAAE4B,QAAQ;QAAEL,KAAK;QAAEQ,UAAU;QAAEQ,SAAS;QAAEV;MAAkB,CAAC,kBACxFrC,OAAA;QACEgD,SAAS,EAAC,gBAAgB;QAC1BvB,KAAK,EAAE;UACLjB,SAAS,EAAEA,SAAS,IAAI,MAAM;UAC9B4B,QAAQ,EAAEA,QAAQ,IAAI,MAAM;UAC5BL,KAAK,EAAEA,KAAK,IAAI,SAAS;UACzBQ,UAAU,EAAEA,UAAU,IAAI,KAAK;UAC/BQ,SAAS,EAAEA,SAAS,IAAI,KAAK;UAC7BV,YAAY,EAAEA,YAAY,IAAI,MAAM;UACpCP,OAAO,EAAE;QACX,CAAE;QAAAG,QAAA,eAEFjC,OAAA;UAAKiD,uBAAuB,EAAE;YAAEC,MAAM,EAAEJ,OAAO,IAAI;UAAyC;QAAE;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9F;IAET,CAAC;IAEDQ,OAAO,EAAE;MACP/C,KAAK,EAAE,SAAS;MAChBC,YAAY,EAAE;QACZ+C,IAAI,EAAE,cAAc;QACpBC,KAAK,EAAE,IAAI;QACX7C,SAAS,EAAE,MAAM;QACjBuB,KAAK,EAAE,SAAS;QAChBgB,SAAS,EAAE,KAAK;QAChBV,YAAY,EAAE;MAChB,CAAC;MACDvB,MAAM,EAAE;QACNsC,IAAI,EAAE;UAAErC,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAe,CAAC;QACtDiD,KAAK,EAAE;UACLtC,IAAI,EAAE,QAAiB;UACvBX,KAAK,EAAE,eAAe;UACtBY,OAAO,EAAE,CACP;YAAEZ,KAAK,EAAE,IAAI;YAAEa,KAAK,EAAE;UAAK,CAAC,EAC5B;YAAEb,KAAK,EAAE,IAAI;YAAEa,KAAK,EAAE;UAAK,CAAC,EAC5B;YAAEb,KAAK,EAAE,IAAI;YAAEa,KAAK,EAAE;UAAK,CAAC,EAC5B;YAAEb,KAAK,EAAE,IAAI;YAAEa,KAAK,EAAE;UAAK,CAAC,EAC5B;YAAEb,KAAK,EAAE,IAAI;YAAEa,KAAK,EAAE;UAAK,CAAC,EAC5B;YAAEb,KAAK,EAAE,IAAI;YAAEa,KAAK,EAAE;UAAK,CAAC;QAEhC,CAAC;QACDT,SAAS,EAAE;UACTO,IAAI,EAAE,QAAiB;UACvBX,KAAK,EAAE,gBAAgB;UACvBY,OAAO,EAAE,CACP;YAAEZ,KAAK,EAAE,MAAM;YAAEa,KAAK,EAAE;UAAO,CAAC,EAChC;YAAEb,KAAK,EAAE,QAAQ;YAAEa,KAAK,EAAE;UAAS,CAAC,EACpC;YAAEb,KAAK,EAAE,OAAO;YAAEa,KAAK,EAAE;UAAQ,CAAC;QAEtC,CAAC;QACDc,KAAK,EAAE;UAAEhB,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAa,CAAC;QACrD2C,SAAS,EAAE;UAAEhC,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAa,CAAC;QACzDiC,YAAY,EAAE;UAAEtB,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAgB;MAChE,CAAC;MACDe,MAAM,EAAEA,CAAC;QAAEiC,IAAI;QAAEC,KAAK;QAAE7C,SAAS;QAAEuB,KAAK;QAAEgB,SAAS;QAAEV;MAAkB,CAAC,KAAK;QAC3E,MAAMiB,UAAU,GAAGD,KAAK,IAAI,IAAI;QAChC,MAAME,SAAoC,GAAG;UAC3CC,EAAE,EAAE,QAAQ;UACZC,EAAE,EAAE,MAAM;UACVC,EAAE,EAAE,SAAS;UACbC,EAAE,EAAE,QAAQ;UACZC,EAAE,EAAE,SAAS;UACbC,EAAE,EAAE;QACN,CAAC;QACD,MAAMzB,QAAQ,GAAGmB,SAAS,CAACF,KAAK,CAAC,IAAI,MAAM;QAE3C,oBAAOjE,KAAK,CAAC0E,aAAa,CACxBR,UAAU,EACV;UACE7B,KAAK,EAAE;YACLjB,SAAS,EAAEA,SAAS,IAAI,MAAM;YAC9BuB,KAAK,EAAEA,KAAK,IAAI,SAAS;YACzBK,QAAQ;YACRE,UAAU,EAAE,MAAM;YAClBS,SAAS,EAAEA,SAAS,IAAI,KAAK;YAC7BV,YAAY,EAAEA,YAAY,IAAI,MAAM;YACpCP,OAAO,EAAE,QAAQ;YACjBS,UAAU,EAAE;UACd;QACF,CAAC,EACDa,IAAI,IAAI,cACV,CAAC;MACH;IACF,CAAC;IACDxD,MAAM,EAAE;MACNQ,KAAK,EAAE,QAAQ;MACfC,YAAY,EAAE;QACZ+C,IAAI,EAAE,aAAa;QACnBW,IAAI,EAAE,GAAG;QACTC,OAAO,EAAE,SAAS;QAClBC,IAAI,EAAE,IAAI;QACVzD,SAAS,EAAE,QAAQ;QACnB0D,SAAS,EAAE,KAAK;QAChBC,MAAM,EAAE;MACV,CAAC;MACDrD,MAAM,EAAE;QACNsC,IAAI,EAAE;UAAErC,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAc,CAAC;QACrD2D,IAAI,EAAE;UAAEhD,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAW,CAAC;QAClD4D,OAAO,EAAE;UACPjD,IAAI,EAAE,QAAiB;UACvBX,KAAK,EAAE,cAAc;UACrBY,OAAO,EAAE,CACP;YAAEZ,KAAK,EAAE,SAAS;YAAEa,KAAK,EAAE;UAAU,CAAC,EACtC;YAAEb,KAAK,EAAE,WAAW;YAAEa,KAAK,EAAE;UAAY,CAAC,EAC1C;YAAEb,KAAK,EAAE,SAAS;YAAEa,KAAK,EAAE;UAAU,CAAC,EACtC;YAAEb,KAAK,EAAE,QAAQ;YAAEa,KAAK,EAAE;UAAS,CAAC,EACpC;YAAEb,KAAK,EAAE,SAAS;YAAEa,KAAK,EAAE;UAAU,CAAC,EACtC;YAAEb,KAAK,EAAE,MAAM;YAAEa,KAAK,EAAE;UAAO,CAAC,EAChC;YAAEb,KAAK,EAAE,OAAO;YAAEa,KAAK,EAAE;UAAQ,CAAC,EAClC;YAAEb,KAAK,EAAE,MAAM;YAAEa,KAAK,EAAE;UAAO,CAAC;QAEpC,CAAC;QACDgD,IAAI,EAAE;UACJlD,IAAI,EAAE,QAAiB;UACvBX,KAAK,EAAE,aAAa;UACpBY,OAAO,EAAE,CACP;YAAEZ,KAAK,EAAE,OAAO;YAAEa,KAAK,EAAE;UAAK,CAAC,EAC/B;YAAEb,KAAK,EAAE,QAAQ;YAAEa,KAAK,EAAE;UAAK,CAAC,EAChC;YAAEb,KAAK,EAAE,OAAO;YAAEa,KAAK,EAAE;UAAK,CAAC;QAEnC,CAAC;QACDT,SAAS,EAAE;UACTO,IAAI,EAAE,QAAiB;UACvBX,KAAK,EAAE,WAAW;UAClBY,OAAO,EAAE,CACP;YAAEZ,KAAK,EAAE,MAAM;YAAEa,KAAK,EAAE;UAAO,CAAC,EAChC;YAAEb,KAAK,EAAE,QAAQ;YAAEa,KAAK,EAAE;UAAS,CAAC,EACpC;YAAEb,KAAK,EAAE,OAAO;YAAEa,KAAK,EAAE;UAAQ,CAAC;QAEtC,CAAC;QACDiD,SAAS,EAAE;UAAEnD,IAAI,EAAE,OAAgB;UAAEX,KAAK,EAAE,YAAY;UAAEY,OAAO,EAAE,CAAC;YAAEZ,KAAK,EAAE,KAAK;YAAEa,KAAK,EAAE;UAAK,CAAC,EAAE;YAAEb,KAAK,EAAE,IAAI;YAAEa,KAAK,EAAE;UAAM,CAAC;QAAE,CAAC;QACnIkD,MAAM,EAAE;UACNpD,IAAI,EAAE,QAAiB;UACvBX,KAAK,EAAE,aAAa;UACpBY,OAAO,EAAE,CACP;YAAEZ,KAAK,EAAE,aAAa;YAAEa,KAAK,EAAE;UAAQ,CAAC,EACxC;YAAEb,KAAK,EAAE,YAAY;YAAEa,KAAK,EAAE;UAAS,CAAC;QAE5C;MACF,CAAC;MACDE,MAAM,EAAEA,CAAC;QAAEiC,IAAI;QAAEW,IAAI;QAAEC,OAAO;QAAEC,IAAI;QAAEzD,SAAS;QAAE0D,SAAS;QAAEC;MAAY,CAAC,KAAK;QAC5E,MAAMC,MAAiC,GAAG;UACxCC,OAAO,EAAE,SAAS;UAClBC,SAAS,EAAE,SAAS;UACpBC,OAAO,EAAE,SAAS;UAClBC,MAAM,EAAE,SAAS;UACjBC,OAAO,EAAE,SAAS;UAClBC,IAAI,EAAE,SAAS;UACfC,KAAK,EAAE,SAAS;UAChBC,IAAI,EAAE;QACR,CAAC;QAED,MAAMC,UAAqC,GAAG;UAC5CR,OAAO,EAAE,OAAO;UAChBC,SAAS,EAAE,OAAO;UAClBC,OAAO,EAAE,OAAO;UAChBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAE,SAAS;UAClBC,IAAI,EAAE,OAAO;UACbC,KAAK,EAAE,SAAS;UAChBC,IAAI,EAAE;QACR,CAAC;QAED,MAAM9C,OAAO,GAAGmC,IAAI,KAAK,IAAI,GAAG,aAAa,GAAGA,IAAI,KAAK,IAAI,GAAG,WAAW,GAAG,gBAAgB;QAC9F,MAAM7B,QAAQ,GAAG6B,IAAI,KAAK,IAAI,GAAG,UAAU,GAAGA,IAAI,KAAK,IAAI,GAAG,UAAU,GAAG,MAAM;QAEjF,oBACEjE,OAAA;UAAKyB,KAAK,EAAE;YAAEK,OAAO,EAAE,MAAM;YAAEtB,SAAS,EAAEA,SAAS,IAAI;UAAS,CAAE;UAAAyB,QAAA,eAChEjC,OAAA;YACE+D,IAAI,EAAEA,IAAI,IAAI,GAAI;YAClBI,MAAM,EAAEA,MAAM,IAAI,OAAQ;YAC1BW,GAAG,EAAEX,MAAM,KAAK,QAAQ,GAAG,qBAAqB,GAAGY,SAAU;YAC7DtD,KAAK,EAAE;cACLC,OAAO,EAAEwC,SAAS,GAAG,OAAO,GAAG,cAAc;cAC7Cc,KAAK,EAAEd,SAAS,GAAG,MAAM,GAAG,MAAM;cAClCpC,OAAO;cACPnB,eAAe,EAAEyD,MAAM,CAACJ,OAAO,CAAC,IAAII,MAAM,CAACC,OAAO;cAClDtC,KAAK,EAAE8C,UAAU,CAACb,OAAO,CAAC,IAAI,OAAO;cACrCiB,cAAc,EAAE,MAAM;cACtBC,YAAY,EAAE,UAAU;cACxB5C,UAAU,EAAE,KAAK;cACjBF,QAAQ;cACR5B,SAAS,EAAE,QAAQ;cACnB2E,UAAU,EAAE,sBAAsB;cAClCC,MAAM,EAAE,MAAM;cACdC,MAAM,EAAE;YACV,CAAE;YACFC,WAAW,EAAGC,CAAC,IAAK;cAClBA,CAAC,CAACC,aAAa,CAAC/D,KAAK,CAACmB,OAAO,GAAG,KAAK;cACrC2C,CAAC,CAACC,aAAa,CAAC/D,KAAK,CAACgE,SAAS,GAAG,kBAAkB;YACtD,CAAE;YACFC,UAAU,EAAGH,CAAC,IAAK;cACjBA,CAAC,CAACC,aAAa,CAAC/D,KAAK,CAACmB,OAAO,GAAG,GAAG;cACnC2C,CAAC,CAACC,aAAa,CAAC/D,KAAK,CAACgE,SAAS,GAAG,eAAe;YACnD,CAAE;YAAAxD,QAAA,EAEDmB,IAAI,IAAI;UAAa;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAEV;IACF,CAAC;IACDgD,KAAK,EAAE;MACLvF,KAAK,EAAE,OAAO;MACdC,YAAY,EAAE;QACZuF,GAAG,EAAE,qCAAqC;QAC1CC,GAAG,EAAE,mBAAmB;QACxBb,KAAK,EAAE,MAAM;QACbc,MAAM,EAAE,MAAM;QACdC,SAAS,EAAE,OAAO;QAClBb,YAAY,EAAE,KAAK;QACnBnC,SAAS,EAAE,KAAK;QAChBV,YAAY,EAAE;MAChB,CAAC;MACDvB,MAAM,EAAE;QACN8E,GAAG,EAAE;UAAE7E,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAY,CAAC;QAClDyF,GAAG,EAAE;UAAE9E,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAW,CAAC;QACjD4E,KAAK,EAAE;UAAEjE,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAQ,CAAC;QAChD0F,MAAM,EAAE;UAAE/E,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAS,CAAC;QAClD2F,SAAS,EAAE;UACThF,IAAI,EAAE,QAAiB;UACvBX,KAAK,EAAE,YAAY;UACnBY,OAAO,EAAE,CACP;YAAEZ,KAAK,EAAE,OAAO;YAAEa,KAAK,EAAE;UAAQ,CAAC,EAClC;YAAEb,KAAK,EAAE,SAAS;YAAEa,KAAK,EAAE;UAAU,CAAC,EACtC;YAAEb,KAAK,EAAE,MAAM;YAAEa,KAAK,EAAE;UAAO,CAAC,EAChC;YAAEb,KAAK,EAAE,MAAM;YAAEa,KAAK,EAAE;UAAO,CAAC,EAChC;YAAEb,KAAK,EAAE,YAAY;YAAEa,KAAK,EAAE;UAAa,CAAC;QAEhD,CAAC;QACDiE,YAAY,EAAE;UAAEnE,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAgB,CAAC;QAC/D2C,SAAS,EAAE;UAAEhC,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAa,CAAC;QACzDiC,YAAY,EAAE;UAAEtB,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAgB;MAChE,CAAC;MACDe,MAAM,EAAEA,CAAC;QAAEyE,GAAG;QAAEC,GAAG;QAAEb,KAAK;QAAEc,MAAM;QAAEC,SAAS;QAAEb,YAAY;QAAEnC,SAAS;QAAEV;MAAkB,CAAC,kBACzFrC,OAAA;QAAKyB,KAAK,EAAE;UAAEK,OAAO,EAAE,MAAM;UAAEiB,SAAS,EAAEA,SAAS,IAAI,KAAK;UAAEV,YAAY,EAAEA,YAAY,IAAI;QAAO,CAAE;QAAAJ,QAAA,eACnGjC,OAAA;UACE4F,GAAG,EAAEA,GAAG,IAAI,qCAAsC;UAClDC,GAAG,EAAEA,GAAG,IAAI,mBAAoB;UAChCpE,KAAK,EAAE;YACLuD,KAAK,EAAEA,KAAK,IAAI,MAAM;YACtBc,MAAM,EAAEA,MAAM,IAAI,MAAM;YACxBC,SAAS,EAAEA,SAAS,IAAI,OAAO;YAC/Bb,YAAY,EAAEA,YAAY,IAAI,KAAK;YACnCxD,OAAO,EAAE,OAAO;YAChBS,QAAQ,EAAE;UACZ;QAAE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAET,CAAC;IAEDlD,SAAS,EAAE;MACTW,KAAK,EAAE,WAAW;MAClBC,YAAY,EAAE;QACZ8B,QAAQ,EAAE,IAAI;QACdL,OAAO,EAAE,MAAM;QACfnB,eAAe,EAAE,aAAa;QAC9BuE,YAAY,EAAE,KAAK;QACnBnC,SAAS,EAAE,KAAK;QAChBV,YAAY,EAAE;MAChB,CAAC;MACDvB,MAAM,EAAE;QACNqB,QAAQ,EAAE;UACRpB,IAAI,EAAE,QAAiB;UACvBX,KAAK,EAAE,WAAW;UAClBY,OAAO,EAAE,CACP;YAAEZ,KAAK,EAAE,eAAe;YAAEa,KAAK,EAAE;UAAK,CAAC,EACvC;YAAEb,KAAK,EAAE,gBAAgB;YAAEa,KAAK,EAAE;UAAK,CAAC,EACxC;YAAEb,KAAK,EAAE,eAAe;YAAEa,KAAK,EAAE;UAAK,CAAC,EACvC;YAAEb,KAAK,EAAE,sBAAsB;YAAEa,KAAK,EAAE;UAAK,CAAC,EAC9C;YAAEb,KAAK,EAAE,YAAY;YAAEa,KAAK,EAAE;UAAQ,CAAC;QAE3C,CAAC;QACDa,OAAO,EAAE;UAAEf,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAU,CAAC;QACpDO,eAAe,EAAE;UAAEI,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAmB,CAAC;QACrE8E,YAAY,EAAE;UAAEnE,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAgB,CAAC;QAC/D2C,SAAS,EAAE;UAAEhC,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAa,CAAC;QACzDiC,YAAY,EAAE;UAAEtB,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAgB;MAChE,CAAC;MACDe,MAAM,EAAEA,CAAC;QAAEgB,QAAQ;QAAEL,OAAO;QAAEnB,eAAe;QAAEuE,YAAY;QAAEnC,SAAS;QAAEV,YAAY;QAAEJ;MAAc,CAAC,KAAK;QACxG,MAAM+D,SAAoC,GAAG;UAC3CC,EAAE,EAAE,OAAO;UACXC,EAAE,EAAE,OAAO;UACXC,EAAE,EAAE,OAAO;UACXC,EAAE,EAAE,QAAQ;UACZC,KAAK,EAAE;QACT,CAAC;QAED,oBACErG,OAAA;UACEyB,KAAK,EAAE;YACLU,QAAQ,EAAE6D,SAAS,CAAC7D,QAAQ,CAAC,IAAI,OAAO;YACxCmE,MAAM,EAAE,QAAQ;YAChBxE,OAAO,EAAEA,OAAO,IAAI,MAAM;YAC1BnB,eAAe,EAAEA,eAAe,IAAI,aAAa;YACjDuE,YAAY,EAAEA,YAAY,IAAI,KAAK;YACnCnC,SAAS,EAAEA,SAAS,IAAI,KAAK;YAC7BV,YAAY,EAAEA,YAAY,IAAI;UAChC,CAAE;UAAAJ,QAAA,EAEDA;QAAQ;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAEV;IACF,CAAC;IAED4D,MAAM,EAAE;MACNnG,KAAK,EAAE,QAAQ;MACfC,YAAY,EAAE;QACZyF,MAAM,EAAE;MACV,CAAC;MACDhF,MAAM,EAAE;QACNgF,MAAM,EAAE;UAAE/E,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAS;MACnD,CAAC;MACDe,MAAM,EAAEA,CAAC;QAAE2E;MAAY,CAAC,kBACtB9F,OAAA;QAAKyB,KAAK,EAAE;UAAEqE,MAAM,EAAEA,MAAM,IAAI,MAAM;UAAEd,KAAK,EAAE;QAAO;MAAE;QAAAxC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAE9D,CAAC;IAED6D,OAAO,EAAE;MACPpG,KAAK,EAAE,SAAS;MAChBC,YAAY,EAAE;QACZ0B,KAAK,EAAE,SAAS;QAChB0E,SAAS,EAAE,KAAK;QAChBhF,KAAK,EAAE,OAAO;QACdsB,SAAS,EAAE,MAAM;QACjBV,YAAY,EAAE;MAChB,CAAC;MACDvB,MAAM,EAAE;QACNiB,KAAK,EAAE;UAAEhB,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAQ,CAAC;QAChDqG,SAAS,EAAE;UAAE1F,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAY,CAAC;QACxDqB,KAAK,EAAE;UACLV,IAAI,EAAE,QAAiB;UACvBX,KAAK,EAAE,OAAO;UACdY,OAAO,EAAE,CACP;YAAEZ,KAAK,EAAE,OAAO;YAAEa,KAAK,EAAE;UAAQ,CAAC,EAClC;YAAEb,KAAK,EAAE,QAAQ;YAAEa,KAAK,EAAE;UAAS,CAAC,EACpC;YAAEb,KAAK,EAAE,QAAQ;YAAEa,KAAK,EAAE;UAAS,CAAC;QAExC,CAAC;QACD8B,SAAS,EAAE;UAAEhC,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAa,CAAC;QACzDiC,YAAY,EAAE;UAAEtB,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAgB;MAChE,CAAC;MACDe,MAAM,EAAEA,CAAC;QAAEY,KAAK;QAAE0E,SAAS;QAAEhF,KAAK;QAAEsB,SAAS;QAAEV;MAAkB,CAAC,kBAChErC,OAAA;QAAKyB,KAAK,EAAE;UAAEK,OAAO,EAAE,QAAQ;UAAEiB,SAAS,EAAEA,SAAS,IAAI,MAAM;UAAEV,YAAY,EAAEA,YAAY,IAAI;QAAO,CAAE;QAAAJ,QAAA,eACtGjC,OAAA;UACEyB,KAAK,EAAE;YACL2D,MAAM,EAAE,MAAM;YACdsB,SAAS,EAAE,GAAGD,SAAS,IAAI,KAAK,IAAIhF,KAAK,IAAI,OAAO,IAAIM,KAAK,IAAI,SAAS,EAAE;YAC5EuE,MAAM,EAAE;UACV;QAAE;UAAA9D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAET,CAAC;IAEDgE,IAAI,EAAE;MACJvG,KAAK,EAAE,MAAM;MACbC,YAAY,EAAE;QACZC,KAAK,EAAE,YAAY;QACnBwC,OAAO,EAAE,kCAAkC;QAC3C8D,QAAQ,EAAE,EAAE;QACZjG,eAAe,EAAE,SAAS;QAC1BkG,WAAW,EAAE,SAAS;QACtB3B,YAAY,EAAE,KAAK;QACnBpD,OAAO,EAAE,QAAQ;QACjBgF,MAAM,EAAE;MACV,CAAC;MACDhG,MAAM,EAAE;QACNR,KAAK,EAAE;UAAES,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAa,CAAC;QACrD0C,OAAO,EAAE;UAAE/B,IAAI,EAAE,UAAmB;UAAEX,KAAK,EAAE;QAAsB,CAAC;QACpEwG,QAAQ,EAAE;UAAE7F,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAuB,CAAC;QAClEO,eAAe,EAAE;UAAEI,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAmB,CAAC;QACrEyG,WAAW,EAAE;UAAE9F,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAe,CAAC;QAC7D8E,YAAY,EAAE;UAAEnE,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAgB,CAAC;QAC/D0B,OAAO,EAAE;UAAEf,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAU,CAAC;QACpD0G,MAAM,EAAE;UAAE/F,IAAI,EAAE,OAAgB;UAAEX,KAAK,EAAE,aAAa;UAAEY,OAAO,EAAE,CAAC;YAAEZ,KAAK,EAAE,KAAK;YAAEa,KAAK,EAAE;UAAK,CAAC,EAAE;YAAEb,KAAK,EAAE,IAAI;YAAEa,KAAK,EAAE;UAAM,CAAC;QAAE;MAClI,CAAC;MACDE,MAAM,EAAEA,CAAC;QAAEb,KAAK;QAAEwC,OAAO;QAAE8D,QAAQ;QAAEjG,eAAe;QAAEkG,WAAW;QAAE3B,YAAY;QAAEpD,OAAO;QAAEgF;MAAY,CAAC,kBACrG9G,OAAA;QAAKyB,KAAK,EAAE;UAAEK,OAAO,EAAE;QAAO,CAAE;QAAAG,QAAA,eAC9BjC,OAAA;UACEyB,KAAK,EAAE;YACLd,eAAe,EAAEA,eAAe,IAAI,SAAS;YAC7CyE,MAAM,EAAE,aAAayB,WAAW,IAAI,SAAS,EAAE;YAC/C3B,YAAY,EAAEA,YAAY,IAAI,KAAK;YACnCpD,OAAO,EAAEA,OAAO,IAAI,QAAQ;YAC5BiF,SAAS,EAAED,MAAM,GAAG,2BAA2B,GAAG,MAAM;YACxDE,QAAQ,EAAE;UACZ,CAAE;UAAA/E,QAAA,GAED2E,QAAQ,iBACP5G,OAAA;YACE4F,GAAG,EAAEgB,QAAS;YACdf,GAAG,EAAEvF,KAAK,IAAI,YAAa;YAC3BmB,KAAK,EAAE;cACLuD,KAAK,EAAE,MAAM;cACbc,MAAM,EAAE,OAAO;cACfC,SAAS,EAAE,OAAO;cAClB1D,YAAY,EAAE,MAAM;cACpB6C,YAAY,EAAE;YAChB;UAAE;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACF,EACArC,KAAK,iBACJN,OAAA;YAAIyB,KAAK,EAAE;cACT6E,MAAM,EAAE,YAAY;cACpBlE,QAAQ,EAAE,SAAS;cACnBE,UAAU,EAAE,MAAM;cAClBP,KAAK,EAAE;YACT,CAAE;YAAAE,QAAA,EACC3B;UAAK;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACL,eACD3C,OAAA;YAAKiD,uBAAuB,EAAE;cAAEC,MAAM,EAAEJ,OAAO,IAAI;YAAmC;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAET,CAAC;IAEDsE,OAAO,EAAE;MACP7G,KAAK,EAAE,SAAS;MAChBC,YAAY,EAAE;QACZ6G,OAAO,EAAE,CAAC;QACVC,GAAG,EAAE,MAAM;QACXtF,UAAU,EAAE;MACd,CAAC;MACDf,MAAM,EAAE;QACNoG,OAAO,EAAE;UACPnG,IAAI,EAAE,QAAiB;UACvBX,KAAK,EAAE,mBAAmB;UAC1BY,OAAO,EAAE,CACP;YAAEZ,KAAK,EAAE,WAAW;YAAEa,KAAK,EAAE;UAAE,CAAC,EAChC;YAAEb,KAAK,EAAE,WAAW;YAAEa,KAAK,EAAE;UAAE,CAAC,EAChC;YAAEb,KAAK,EAAE,WAAW;YAAEa,KAAK,EAAE;UAAE,CAAC;QAEpC,CAAC;QACDkG,GAAG,EAAE;UAAEpG,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAsB,CAAC;QAC5DyB,UAAU,EAAE;UACVd,IAAI,EAAE,QAAiB;UACvBX,KAAK,EAAE,oBAAoB;UAC3BY,OAAO,EAAE,CACP;YAAEZ,KAAK,EAAE,SAAS;YAAEa,KAAK,EAAE;UAAU,CAAC,EACtC;YAAEb,KAAK,EAAE,KAAK;YAAEa,KAAK,EAAE;UAAa,CAAC,EACrC;YAAEb,KAAK,EAAE,QAAQ;YAAEa,KAAK,EAAE;UAAS,CAAC,EACpC;YAAEb,KAAK,EAAE,QAAQ;YAAEa,KAAK,EAAE;UAAW,CAAC;QAE1C;MACF,CAAC;MACDE,MAAM,EAAEA,CAAC;QAAE+F,OAAO;QAAEC,GAAG;QAAEtF,UAAU;QAAEI;MAAc,CAAC,kBAClDjC,OAAA;QAAKyB,KAAK,EAAE;UAAEK,OAAO,EAAE;QAAO,CAAE;QAAAG,QAAA,eAC9BjC,OAAA;UACEyB,KAAK,EAAE;YACLC,OAAO,EAAE,MAAM;YACf0F,mBAAmB,EAAE,UAAUF,OAAO,IAAI,CAAC,QAAQ;YACnDC,GAAG,EAAEA,GAAG,IAAI,MAAM;YAClBtF,UAAU,EAAEA,UAAU,IAAI;UAC5B,CAAE;UAAAI,QAAA,EAEDA;QAAQ;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAET,CAAC;IAED0E,IAAI,EAAE;MACJjH,KAAK,EAAE,MAAM;MACbC,YAAY,EAAE;QACZiH,KAAK,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC;QACpDC,QAAQ,EAAE,IAAI;QACdxF,KAAK,EAAE,SAAS;QAChBK,QAAQ,EAAE;MACZ,CAAC;MACDtB,MAAM,EAAE;QACNwG,KAAK,EAAE;UAAEvG,IAAI,EAAE,OAAgB;UAAEX,KAAK,EAAE,YAAY;UAAEoH,WAAW,EAAE;YAAEC,IAAI,EAAE;cAAE1G,IAAI,EAAE;YAAgB;UAAE;QAAE,CAAC;QACxGwG,QAAQ,EAAE;UACRxG,IAAI,EAAE,QAAiB;UACvBX,KAAK,EAAE,WAAW;UAClBY,OAAO,EAAE,CACP;YAAEZ,KAAK,EAAE,qBAAqB;YAAEa,KAAK,EAAE;UAAK,CAAC,EAC7C;YAAEb,KAAK,EAAE,mBAAmB;YAAEa,KAAK,EAAE;UAAK,CAAC;QAE/C,CAAC;QACDc,KAAK,EAAE;UAAEhB,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAa,CAAC;QACrDgC,QAAQ,EAAE;UAAErB,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAY;MACxD,CAAC;MACDe,MAAM,EAAEA,CAAC;QAAEmG,KAAK;QAAEC,QAAQ;QAAExF,KAAK;QAAEK;MAAc,CAAC,KAAK;QACrD,MAAMsF,OAAO,GAAGH,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI;QAC/C,MAAMI,UAAU,GAAGC,KAAK,CAACC,OAAO,CAACP,KAAK,CAAC,GAAGA,KAAK,GAAG,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC;QAE/F,oBACEtH,OAAA;UAAKyB,KAAK,EAAE;YAAEK,OAAO,EAAE;UAAO,CAAE;UAAAG,QAAA,eAC9BjC,OAAA,CAAC0H,OAAO;YACNjG,KAAK,EAAE;cACLM,KAAK,EAAEA,KAAK,IAAI,SAAS;cACzBK,QAAQ,EAAEA,QAAQ,IAAI,MAAM;cAC5BG,UAAU,EAAE,KAAK;cACjBuF,WAAW,EAAE;YACf,CAAE;YAAA7F,QAAA,EAED0F,UAAU,CAACI,GAAG,CAAC,CAACN,IAAY,EAAEO,KAAa,kBAC1ChI,OAAA;cAAgByB,KAAK,EAAE;gBAAEY,YAAY,EAAE;cAAS,CAAE;cAAAJ,QAAA,EAC/CwF;YAAI,GADEO,KAAK;cAAAxF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAEV;IACF;EACF;AACF,CAAC;AAMD,MAAMsF,UAAqC,GAAGA,CAAC;EAAEjF;AAAU,CAAC,KAAK;EAAAkF,EAAA;EAC/D,MAAMC,QAAQ,GAAG3I,WAAW,CAAC,CAAC;EAE9B,MAAM,CAAC4I,QAAQ,EAAEC,WAAW,CAAC,GAAGhJ,QAAQ,CAAM;IAC5CyD,OAAO,EAAE,EAAE;IACXwF,IAAI,EAAE;MACJhI,KAAK,EAAE,UAAU;MACjBiI,eAAe,EAAE;IACnB;EACF,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGpJ,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqJ,MAAM,EAAEC,SAAS,CAAC,GAAGtJ,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACuJ,KAAK,EAAEC,QAAQ,CAAC,GAAGxJ,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACyJ,OAAO,EAAEC,UAAU,CAAC,GAAG1J,QAAQ,CAAC,KAAK,CAAC;EAE7CC,SAAS,CAAC,MAAM;IACd0J,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA1J,SAAS,CAAC,MAAM;IACd,MAAM2J,OAAO,GAAGnJ,4BAA4B,CAAC,CAAC;;IAE9C;IACA,MAAMoJ,eAAe,GAAIC,KAAiB,IAAK;MAC7C;MACA,IAAIA,KAAK,CAACC,OAAO,KACfD,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,gBAAgB,CAAC,IACxCF,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,gCAAgC,CAAC,IACxDF,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,sBAAsB,CAAC,IAC9CF,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,0BAA0B,CAAC,IAClDF,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,yBAAyB,CAAC,CAClD,EAAE;QACDF,KAAK,CAACG,cAAc,CAAC,CAAC;QACtB,OAAO,KAAK;MACd;MACA,OAAO,IAAI;IACb,CAAC;IAEDC,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAEN,eAAe,CAAC;IAEjD,OAAO,MAAM;MACXD,OAAO,CAAC,CAAC;MACTM,MAAM,CAACE,mBAAmB,CAAC,OAAO,EAAEP,eAAe,CAAC;IACtD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMF,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChBI,QAAQ,CAAC,EAAE,CAAC;;MAEZ;MACA,MAAMa,WAAW,GAAG,MAAM7J,WAAW,CAACmJ,gBAAgB,CAAC,CAAC;MACxD,IAAI,CAACU,WAAW,CAACC,QAAQ,EAAE;QACzBd,QAAQ,CAAC,wDAAwD,CAAC;QAClEJ,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MACAM,UAAU,CAAC,IAAI,CAAC;IAClB,CAAC,CAAC,OAAOa,GAAQ,EAAE;MACjBC,OAAO,CAACjB,KAAK,CAAC,8BAA8B,EAAEgB,GAAG,CAAC;MAClDf,QAAQ,CAACe,GAAG,CAACR,OAAO,IAAI,+BAA+B,CAAC;IAC1D,CAAC,SAAS;MACRX,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqB,UAAU,GAAG,MAAOC,IAAS,IAAK;IACtC,IAAI;MACFpB,SAAS,CAAC,IAAI,CAAC;MACfE,QAAQ,CAAC,EAAE,CAAC;;MAEZ;MACA;MACAmB,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAEC,IAAI,CAACC,SAAS,CAACJ,IAAI,CAAC,CAAC;;MAE9D;MACAK,KAAK,CAAC,gEAAgE,CAAC;;MAEvE;MACA/B,WAAW,CAAC0B,IAAI,CAAC;IACnB,CAAC,CAAC,OAAOH,GAAQ,EAAE;MACjBC,OAAO,CAACjB,KAAK,CAAC,sBAAsB,EAAEgB,GAAG,CAAC;MAC1Cf,QAAQ,CAACe,GAAG,CAACR,OAAO,IAAI,uBAAuB,CAAC;IAClD,CAAC,SAAS;MACRT,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,MAAM0B,YAAY,GAAGA,CAAA,KAAM;IACzBlC,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAED,IAAIK,OAAO,EAAE;IACX,oBACExI,OAAA,CAACP,SAAS;MAACuD,SAAS,EAAC,kBAAkB;MAAAf,QAAA,gBACrCjC,OAAA,CAACL,OAAO;QAAC2K,SAAS,EAAC,QAAQ;QAACC,IAAI,EAAC,QAAQ;QAAAtI,QAAA,eACvCjC,OAAA;UAAMgD,SAAS,EAAC,iBAAiB;UAAAf,QAAA,EAAC;QAAU;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eACV3C,OAAA;QAAGgD,SAAS,EAAC,MAAM;QAAAf,QAAA,EAAC;MAAwB;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC;EAEhB;EAEA,IAAIiG,KAAK,EAAE;IACT,oBACE5I,OAAA,CAACP,SAAS;MAACuD,SAAS,EAAC,MAAM;MAAAf,QAAA,eACzBjC,OAAA,CAACN,KAAK;QAACsE,OAAO,EAAC,QAAQ;QAAA/B,QAAA,gBACrBjC,OAAA,CAACN,KAAK,CAACyD,OAAO;UAAAlB,QAAA,EAAC;QAAK;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC,eACpC3C,OAAA;UAAAiC,QAAA,EAAI2G;QAAK;UAAApG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACd3C,OAAA,CAACJ,MAAM;UAACoE,OAAO,EAAC,gBAAgB;UAACwG,OAAO,EAAEA,CAAA,KAAMrC,QAAQ,CAAC,GAAG,CAAE;UAAAlG,QAAA,EAAC;QAE/D;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEhB;EAEA,IAAI,CAACmG,OAAO,EAAE;IACZ,oBACE9I,OAAA,CAACP,SAAS;MAACuD,SAAS,EAAC,MAAM;MAAAf,QAAA,eACzBjC,OAAA,CAACN,KAAK;QAACsE,OAAO,EAAC,SAAS;QAAA/B,QAAA,gBACtBjC,OAAA,CAACN,KAAK,CAACyD,OAAO;UAAAlB,QAAA,EAAC;QAAa;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC,eAC5C3C,OAAA;UAAAiC,QAAA,EAAG;QAAsD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC7D3C,OAAA,CAACJ,MAAM;UAACoE,OAAO,EAAC,iBAAiB;UAACwG,OAAO,EAAEA,CAAA,KAAMrC,QAAQ,CAAC,GAAG,CAAE;UAAAlG,QAAA,EAAC;QAEhE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEhB;EAEA,IAAI,CAACyF,QAAQ,EAAE;IACb,oBACEpI,OAAA,CAACP,SAAS;MAACuD,SAAS,EAAC,MAAM;MAAAf,QAAA,eACzBjC,OAAA,CAACN,KAAK;QAACsE,OAAO,EAAC,SAAS;QAAA/B,QAAA,gBACtBjC,OAAA,CAACN,KAAK,CAACyD,OAAO;UAAAlB,QAAA,EAAC;QAAgB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC,eAC/C3C,OAAA;UAAAiC,QAAA,EAAG;QAAmC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC1C3C,OAAA,CAACJ,MAAM;UAACoE,OAAO,EAAC,iBAAiB;UAACwG,OAAO,EAAEA,CAAA,KAAMrC,QAAQ,CAAC,GAAG,CAAE;UAAAlG,QAAA,EAAC;QAEhE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEhB;EAEA,oBACE3C,OAAA;IAAKgD,SAAS,EAAC,uBAAuB;IAACvB,KAAK,EAAE;MAAEqE,MAAM,EAAE,OAAO;MAAEd,KAAK,EAAE,OAAO;MAAEhD,QAAQ,EAAE,OAAO;MAAEyI,GAAG,EAAE,CAAC;MAAEC,IAAI,EAAE,CAAC;MAAExI,MAAM,EAAE;IAAK,CAAE;IAAAD,QAAA,gBAClIjC,OAAA,CAACT,IAAI;MACHoL,MAAM,EAAE1K,UAAW;MACnB8J,IAAI,EAAE3B,QAAS;MACfwC,SAAS,EAAEd,UAAW;MACtBe,QAAQ,EAAExC,WAAY;MACtByC,WAAW,EAAC,eAAe;MAC3BC,mBAAmB,EAAEA,CAAA,kBACnB/K,OAAA;QAAKgD,SAAS,EAAC,qBAAqB;QAAAf,QAAA,gBAClCjC,OAAA,CAACJ,MAAM;UACLoE,OAAO,EAAC,SAAS;UACjBC,IAAI,EAAC,IAAI;UACTuG,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC1B,QAAQ,CAAE;UACpC4C,QAAQ,EAAEtC,MAAO;UAAAzG,QAAA,EAEhByG,MAAM,GAAG,WAAW,GAAG;QAAM;UAAAlG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eACT3C,OAAA,CAACJ,MAAM;UACLoE,OAAO,EAAC,mBAAmB;UAC3BC,IAAI,EAAC,IAAI;UACTuG,OAAO,EAAEH,YAAa;UACtBW,QAAQ,EAAEtC,MAAO;UAAAzG,QAAA,EAClB;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IACL;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EACD+F,MAAM,iBACL1I,OAAA;MACEyB,KAAK,EAAE;QACLO,QAAQ,EAAE,OAAO;QACjByI,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPO,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTvK,eAAe,EAAE,oBAAoB;QACrCe,OAAO,EAAE,MAAM;QACfG,UAAU,EAAE,QAAQ;QACpBD,cAAc,EAAE,QAAQ;QACxBM,MAAM,EAAE;MACV,CAAE;MAAAD,QAAA,eAEFjC,OAAA;QAAKyB,KAAK,EAAE;UAAEd,eAAe,EAAE,OAAO;UAAEmB,OAAO,EAAE,MAAM;UAAEoD,YAAY,EAAE;QAAS,CAAE;QAAAjD,QAAA,gBAChFjC,OAAA,CAACL,OAAO;UAAC2K,SAAS,EAAC,QAAQ;UAACtH,SAAS,EAAC;QAAM;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,oBAEjD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACuF,EAAA,CAvMID,UAAqC;EAAA,QACxBzI,WAAW;AAAA;AAAA2L,EAAA,GADxBlD,UAAqC;AAyM3C,eAAeA,UAAU;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}