import React, { useState, useEffect } from 'react';
import { Puck } from '@measured/puck';
import '@measured/puck/puck.css';
import './PuckEditor.css';
import { useNavigate } from 'react-router-dom';
import { Container, <PERSON><PERSON>, Spin<PERSON>, <PERSON><PERSON> } from 'react-bootstrap';
import authService from '../../services/authService';
import { suppressResizeObserverErrors } from '../../utils/errorHandlers';

// Comprehensive Puck configuration with all essential components
const puckConfig = {
  components: {
    // Layout Components
    Hero: {
      label: 'Hero Section',
      defaultProps: {
        title: 'Hero Title',
        subtitle: 'Hero subtitle text',
        textAlign: 'center',
        minHeight: '400px',
        backgroundType: 'gradient',
        backgroundColor: '#667eea',
        backgroundGradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        textColor: '#ffffff',
      },
      fields: {
        title: { type: 'text' as const, label: 'Title' },
        subtitle: { type: 'textarea' as const, label: 'Subtitle' },
        textAlign: {
          type: 'select' as const,
          label: 'Text Alignment',
          options: [
            { label: 'Left', value: 'left' },
            { label: 'Center', value: 'center' },
            { label: 'Right', value: 'right' },
          ],
        },
        minHeight: { type: 'text' as const, label: 'Min Height (px)' },
        backgroundType: {
          type: 'select' as const,
          label: 'Background Type',
          options: [
            { label: 'Solid Color', value: 'solid' },
            { label: 'Gradient', value: 'gradient' },
            { label: 'Image', value: 'image' },
          ],
        },
        backgroundColor: { type: 'text' as const, label: 'Background Color' },
        backgroundGradient: { type: 'text' as const, label: 'Background Gradient' },
        backgroundImage: { type: 'text' as const, label: 'Background Image URL' },
        textColor: { type: 'text' as const, label: 'Text Color' },
      },
      render: ({ title, subtitle, textAlign, minHeight, backgroundType, backgroundColor, backgroundGradient, backgroundImage, textColor }: any) => {
        let backgroundStyle = {};

        switch (backgroundType) {
          case 'solid':
            backgroundStyle = { backgroundColor: backgroundColor || '#667eea' };
            break;
          case 'gradient':
            backgroundStyle = { background: backgroundGradient || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' };
            break;
          case 'image':
            backgroundStyle = {
              backgroundImage: `url(${backgroundImage})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat'
            };
            break;
          default:
            backgroundStyle = { background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' };
        }

        return (
          <section
            style={{
              minHeight: minHeight || '400px',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: textAlign === 'center' ? 'center' : textAlign === 'right' ? 'flex-end' : 'flex-start',
              textAlign: textAlign || 'center',
              padding: '4rem 2rem',
              color: textColor || 'white',
              position: 'relative',
              ...backgroundStyle,
            }}
          >
            <div style={{ position: 'relative', zIndex: 1, maxWidth: '800px' }}>
              <h1 style={{
                fontSize: 'clamp(2rem, 5vw, 3.5rem)',
                marginBottom: '1rem',
                fontWeight: 'bold',
                lineHeight: 1.2,
              }}>
                {title || 'Hero Title'}
              </h1>
              <p style={{
                fontSize: 'clamp(1rem, 2.5vw, 1.25rem)',
                opacity: 0.9,
                lineHeight: 1.6,
                marginBottom: '2rem',
              }}>
                {subtitle || 'Hero subtitle text'}
              </p>
            </div>
          </section>
        );
      },
    },
    // Content Components
    Text: {
      label: 'Text Block',
      defaultProps: {
        content: '<p>Enter your text content here...</p>',
        textAlign: 'left',
        fontSize: '16px',
        color: '#333333',
        lineHeight: '1.6',
        marginTop: '0px',
        marginBottom: '20px',
      },
      fields: {
        content: { type: 'textarea' as const, label: 'Content (HTML)' },
        textAlign: {
          type: 'select' as const,
          label: 'Text Alignment',
          options: [
            { label: 'Left', value: 'left' },
            { label: 'Center', value: 'center' },
            { label: 'Right', value: 'right' },
            { label: 'Justify', value: 'justify' },
          ],
        },
        fontSize: { type: 'text' as const, label: 'Font Size' },
        color: { type: 'text' as const, label: 'Text Color' },
        lineHeight: { type: 'text' as const, label: 'Line Height' },
        marginTop: { type: 'text' as const, label: 'Margin Top' },
        marginBottom: { type: 'text' as const, label: 'Margin Bottom' },
      },
      render: ({ content, textAlign, fontSize, color, lineHeight, marginTop, marginBottom }: any) => (
        <div
          className="text-component"
          style={{
            textAlign: textAlign || 'left',
            fontSize: fontSize || '16px',
            color: color || '#333333',
            lineHeight: lineHeight || '1.6',
            marginTop: marginTop || '0px',
            marginBottom: marginBottom || '20px',
            padding: '0 1rem',
          }}
        >
          <div dangerouslySetInnerHTML={{ __html: content || '<p>Enter your text content here...</p>' }} />
        </div>
      ),
    },

    Heading: {
      label: 'Heading',
      defaultProps: {
        text: 'Heading Text',
        level: 'h2',
        textAlign: 'left',
        color: '#333333',
        marginTop: '0px',
        marginBottom: '20px',
      },
      fields: {
        text: { type: 'text' as const, label: 'Heading Text' },
        level: {
          type: 'select' as const,
          label: 'Heading Level',
          options: [
            { label: 'H1', value: 'h1' },
            { label: 'H2', value: 'h2' },
            { label: 'H3', value: 'h3' },
            { label: 'H4', value: 'h4' },
            { label: 'H5', value: 'h5' },
            { label: 'H6', value: 'h6' },
          ],
        },
        textAlign: {
          type: 'select' as const,
          label: 'Text Alignment',
          options: [
            { label: 'Left', value: 'left' },
            { label: 'Center', value: 'center' },
            { label: 'Right', value: 'right' },
          ],
        },
        color: { type: 'text' as const, label: 'Text Color' },
        marginTop: { type: 'text' as const, label: 'Margin Top' },
        marginBottom: { type: 'text' as const, label: 'Margin Bottom' },
      },
      render: ({ text, level, textAlign, color, marginTop, marginBottom }: any) => {
        const HeadingTag = level || 'h2';
        const fontSizes: { [key: string]: string } = {
          h1: '2.5rem',
          h2: '2rem',
          h3: '1.75rem',
          h4: '1.5rem',
          h5: '1.25rem',
          h6: '1rem',
        };
        const fontSize = fontSizes[level] || '2rem';

        return React.createElement(
          HeadingTag,
          {
            style: {
              textAlign: textAlign || 'left',
              color: color || '#333333',
              fontSize,
              fontWeight: 'bold',
              marginTop: marginTop || '0px',
              marginBottom: marginBottom || '20px',
              padding: '0 1rem',
              lineHeight: 1.2,
            }
          },
          text || 'Heading Text'
        );
      },
    },
    Button: {
      label: 'Button',
      defaultProps: {
        text: 'Button Text',
        href: '#',
        variant: 'primary',
        size: 'md',
        textAlign: 'center',
        fullWidth: false,
        target: '_self',
      },
      fields: {
        text: { type: 'text' as const, label: 'Button Text' },
        href: { type: 'text' as const, label: 'Link URL' },
        variant: {
          type: 'select' as const,
          label: 'Button Style',
          options: [
            { label: 'Primary', value: 'primary' },
            { label: 'Secondary', value: 'secondary' },
            { label: 'Success', value: 'success' },
            { label: 'Danger', value: 'danger' },
            { label: 'Warning', value: 'warning' },
            { label: 'Info', value: 'info' },
            { label: 'Light', value: 'light' },
            { label: 'Dark', value: 'dark' },
          ],
        },
        size: {
          type: 'select' as const,
          label: 'Button Size',
          options: [
            { label: 'Small', value: 'sm' },
            { label: 'Medium', value: 'md' },
            { label: 'Large', value: 'lg' },
          ],
        },
        textAlign: {
          type: 'select' as const,
          label: 'Alignment',
          options: [
            { label: 'Left', value: 'left' },
            { label: 'Center', value: 'center' },
            { label: 'Right', value: 'right' },
          ],
        },
        fullWidth: { type: 'radio' as const, label: 'Full Width', options: [{ label: 'Yes', value: true }, { label: 'No', value: false }] },
        target: {
          type: 'select' as const,
          label: 'Link Target',
          options: [
            { label: 'Same Window', value: '_self' },
            { label: 'New Window', value: '_blank' },
          ],
        },
      },
      render: ({ text, href, variant, size, textAlign, fullWidth, target }: any) => {
        const colors: { [key: string]: string } = {
          primary: '#007bff',
          secondary: '#6c757d',
          success: '#28a745',
          danger: '#dc3545',
          warning: '#ffc107',
          info: '#17a2b8',
          light: '#f8f9fa',
          dark: '#343a40',
        };

        const textColors: { [key: string]: string } = {
          primary: 'white',
          secondary: 'white',
          success: 'white',
          danger: 'white',
          warning: '#212529',
          info: 'white',
          light: '#212529',
          dark: 'white',
        };

        const padding = size === 'sm' ? '0.5rem 1rem' : size === 'lg' ? '1rem 2rem' : '0.75rem 1.5rem';
        const fontSize = size === 'sm' ? '0.875rem' : size === 'lg' ? '1.125rem' : '1rem';

        return (
          <div style={{ padding: '1rem', textAlign: textAlign || 'center' }}>
            <a
              href={href || '#'}
              target={target || '_self'}
              rel={target === '_blank' ? 'noopener noreferrer' : undefined}
              style={{
                display: fullWidth ? 'block' : 'inline-block',
                width: fullWidth ? '100%' : 'auto',
                padding,
                backgroundColor: colors[variant] || colors.primary,
                color: textColors[variant] || 'white',
                textDecoration: 'none',
                borderRadius: '0.375rem',
                fontWeight: '500',
                fontSize,
                textAlign: 'center',
                transition: 'all 0.2s ease-in-out',
                border: 'none',
                cursor: 'pointer',
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.opacity = '0.9';
                e.currentTarget.style.transform = 'translateY(-1px)';
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.opacity = '1';
                e.currentTarget.style.transform = 'translateY(0)';
              }}
            >
              {text || 'Button Text'}
            </a>
          </div>
        );
      },
    },
    Image: {
      label: 'Image',
      defaultProps: {
        src: 'https://via.placeholder.com/600x400',
        alt: 'Image description',
        width: '100%',
        height: 'auto',
        objectFit: 'cover',
        borderRadius: '0px',
        marginTop: '0px',
        marginBottom: '20px',
      },
      fields: {
        src: { type: 'text' as const, label: 'Image URL' },
        alt: { type: 'text' as const, label: 'Alt Text' },
        width: { type: 'text' as const, label: 'Width' },
        height: { type: 'text' as const, label: 'Height' },
        objectFit: {
          type: 'select' as const,
          label: 'Object Fit',
          options: [
            { label: 'Cover', value: 'cover' },
            { label: 'Contain', value: 'contain' },
            { label: 'Fill', value: 'fill' },
            { label: 'None', value: 'none' },
            { label: 'Scale Down', value: 'scale-down' },
          ],
        },
        borderRadius: { type: 'text' as const, label: 'Border Radius' },
        marginTop: { type: 'text' as const, label: 'Margin Top' },
        marginBottom: { type: 'text' as const, label: 'Margin Bottom' },
      },
      render: ({ src, alt, width, height, objectFit, borderRadius, marginTop, marginBottom }: any) => (
        <div style={{ padding: '1rem', marginTop: marginTop || '0px', marginBottom: marginBottom || '20px' }}>
          <img
            src={src || 'https://via.placeholder.com/600x400'}
            alt={alt || 'Image description'}
            style={{
              width: width || '100%',
              height: height || 'auto',
              objectFit: objectFit || 'cover',
              borderRadius: borderRadius || '0px',
              display: 'block',
              maxWidth: '100%',
            }}
          />
        </div>
      ),
    },

    Container: {
      label: 'Container',
      defaultProps: {
        maxWidth: 'lg',
        padding: '2rem',
        backgroundColor: 'transparent',
        borderRadius: '0px',
        marginTop: '0px',
        marginBottom: '0px',
      },
      fields: {
        maxWidth: {
          type: 'select' as const,
          label: 'Max Width',
          options: [
            { label: 'Small (540px)', value: 'sm' },
            { label: 'Medium (720px)', value: 'md' },
            { label: 'Large (960px)', value: 'lg' },
            { label: 'Extra Large (1140px)', value: 'xl' },
            { label: 'Full Width', value: 'fluid' },
          ],
        },
        padding: { type: 'text' as const, label: 'Padding' },
        backgroundColor: { type: 'text' as const, label: 'Background Color' },
        borderRadius: { type: 'text' as const, label: 'Border Radius' },
        marginTop: { type: 'text' as const, label: 'Margin Top' },
        marginBottom: { type: 'text' as const, label: 'Margin Bottom' },
      },
      render: ({ maxWidth, padding, backgroundColor, borderRadius, marginTop, marginBottom, children }: any) => {
        const maxWidths: { [key: string]: string } = {
          sm: '540px',
          md: '720px',
          lg: '960px',
          xl: '1140px',
          fluid: '100%',
        };

        return (
          <div
            style={{
              maxWidth: maxWidths[maxWidth] || '960px',
              margin: '0 auto',
              padding: padding || '2rem',
              backgroundColor: backgroundColor || 'transparent',
              borderRadius: borderRadius || '0px',
              marginTop: marginTop || '0px',
              marginBottom: marginBottom || '0px',
            }}
          >
            {children}
          </div>
        );
      },
    },

    Spacer: {
      label: 'Spacer',
      defaultProps: {
        height: '40px',
      },
      fields: {
        height: { type: 'text' as const, label: 'Height' },
      },
      render: ({ height }: any) => (
        <div style={{ height: height || '40px', width: '100%' }} />
      ),
    },

    Divider: {
      label: 'Divider',
      defaultProps: {
        color: '#e0e0e0',
        thickness: '1px',
        style: 'solid',
        marginTop: '20px',
        marginBottom: '20px',
      },
      fields: {
        color: { type: 'text' as const, label: 'Color' },
        thickness: { type: 'text' as const, label: 'Thickness' },
        style: {
          type: 'select' as const,
          label: 'Style',
          options: [
            { label: 'Solid', value: 'solid' },
            { label: 'Dashed', value: 'dashed' },
            { label: 'Dotted', value: 'dotted' },
          ],
        },
        marginTop: { type: 'text' as const, label: 'Margin Top' },
        marginBottom: { type: 'text' as const, label: 'Margin Bottom' },
      },
      render: ({ color, thickness, style, marginTop, marginBottom }: any) => (
        <div style={{ padding: '0 1rem', marginTop: marginTop || '20px', marginBottom: marginBottom || '20px' }}>
          <hr
            style={{
              border: 'none',
              borderTop: `${thickness || '1px'} ${style || 'solid'} ${color || '#e0e0e0'}`,
              margin: 0,
            }}
          />
        </div>
      ),
    },

    Card: {
      label: 'Card',
      defaultProps: {
        title: 'Card Title',
        content: '<p>Card content goes here...</p>',
        imageUrl: '',
        backgroundColor: '#ffffff',
        borderColor: '#e0e0e0',
        borderRadius: '8px',
        padding: '1.5rem',
        shadow: true,
      },
      fields: {
        title: { type: 'text' as const, label: 'Card Title' },
        content: { type: 'textarea' as const, label: 'Card Content (HTML)' },
        imageUrl: { type: 'text' as const, label: 'Image URL (optional)' },
        backgroundColor: { type: 'text' as const, label: 'Background Color' },
        borderColor: { type: 'text' as const, label: 'Border Color' },
        borderRadius: { type: 'text' as const, label: 'Border Radius' },
        padding: { type: 'text' as const, label: 'Padding' },
        shadow: { type: 'radio' as const, label: 'Drop Shadow', options: [{ label: 'Yes', value: true }, { label: 'No', value: false }] },
      },
      render: ({ title, content, imageUrl, backgroundColor, borderColor, borderRadius, padding, shadow }: any) => (
        <div style={{ padding: '1rem' }}>
          <div
            style={{
              backgroundColor: backgroundColor || '#ffffff',
              border: `1px solid ${borderColor || '#e0e0e0'}`,
              borderRadius: borderRadius || '8px',
              padding: padding || '1.5rem',
              boxShadow: shadow ? '0 2px 4px rgba(0,0,0,0.1)' : 'none',
              overflow: 'hidden',
            }}
          >
            {imageUrl && (
              <img
                src={imageUrl}
                alt={title || 'Card image'}
                style={{
                  width: '100%',
                  height: '200px',
                  objectFit: 'cover',
                  marginBottom: '1rem',
                  borderRadius: '4px',
                }}
              />
            )}
            {title && (
              <h3 style={{
                margin: '0 0 1rem 0',
                fontSize: '1.25rem',
                fontWeight: 'bold',
                color: '#333',
              }}>
                {title}
              </h3>
            )}
            <div dangerouslySetInnerHTML={{ __html: content || '<p>Card content goes here...</p>' }} />
          </div>
        </div>
      ),
    },

    Columns: {
      label: 'Columns',
      defaultProps: {
        columns: 2,
        gap: '2rem',
        alignItems: 'stretch',
      },
      fields: {
        columns: {
          type: 'select' as const,
          label: 'Number of Columns',
          options: [
            { label: '2 Columns', value: 2 },
            { label: '3 Columns', value: 3 },
            { label: '4 Columns', value: 4 },
          ],
        },
        gap: { type: 'text' as const, label: 'Gap Between Columns' },
        alignItems: {
          type: 'select' as const,
          label: 'Vertical Alignment',
          options: [
            { label: 'Stretch', value: 'stretch' },
            { label: 'Top', value: 'flex-start' },
            { label: 'Center', value: 'center' },
            { label: 'Bottom', value: 'flex-end' },
          ],
        },
      },
      render: ({ columns, gap, alignItems, children }: any) => (
        <div style={{ padding: '1rem' }}>
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: `repeat(${columns || 2}, 1fr)`,
              gap: gap || '2rem',
              alignItems: alignItems || 'stretch',
            }}
          >
            {children}
          </div>
        </div>
      ),
    },

    List: {
      label: 'List',
      defaultProps: {
        items: ['List item 1', 'List item 2', 'List item 3'],
        listType: 'ul',
        color: '#333333',
        fontSize: '16px',
      },
      fields: {
        items: { type: 'array' as const, label: 'List Items', arrayFields: { item: { type: 'text' as const } } },
        listType: {
          type: 'select' as const,
          label: 'List Type',
          options: [
            { label: 'Unordered (bullets)', value: 'ul' },
            { label: 'Ordered (numbers)', value: 'ol' },
          ],
        },
        color: { type: 'text' as const, label: 'Text Color' },
        fontSize: { type: 'text' as const, label: 'Font Size' },
      },
      render: ({ items, listType, color, fontSize }: any) => {
        const ListTag = listType === 'ol' ? 'ol' : 'ul';
        const itemsArray = Array.isArray(items) ? items : ['List item 1', 'List item 2', 'List item 3'];

        return (
          <div style={{ padding: '1rem' }}>
            <ListTag
              style={{
                color: color || '#333333',
                fontSize: fontSize || '16px',
                lineHeight: '1.6',
                paddingLeft: '1.5rem',
              }}
            >
              {itemsArray.map((item: string, index: number) => (
                <li key={index} style={{ marginBottom: '0.5rem' }}>
                  {item}
                </li>
              ))}
            </ListTag>
          </div>
        );
      },
    },
  },
};

interface PuckEditorProps {
  className?: string;
}

const PuckEditor: React.FC<PuckEditorProps> = ({ className }) => {
  const navigate = useNavigate();

  const [puckData, setPuckData] = useState<any>({
    content: [],
    root: {
      title: 'New Page',
      metaDescription: '',
    },
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string>('');
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    checkAdminStatus();
  }, []);

  // Handle ResizeObserver errors more gracefully
  useEffect(() => {
    const cleanup = suppressResizeObserverErrors();

    // Additional error handling for Puck-specific issues
    const handlePuckError = (event: ErrorEvent) => {
      // Suppress common Puck-related errors that don't affect functionality
      if (event.message && (
        event.message.includes('ResizeObserver') ||
        event.message.includes('Cannot read properties of null') ||
        event.message.includes('Cannot read property') ||
        event.message.includes('reading \'offsetHeight\'') ||
        event.message.includes('reading \'offsetWidth\'')
      )) {
        event.preventDefault();
        return false;
      }
      return true;
    };

    window.addEventListener('error', handlePuckError);

    return () => {
      cleanup();
      window.removeEventListener('error', handlePuckError);
    };
  }, []);

  const checkAdminStatus = async () => {
    try {
      setLoading(true);
      setError('');

      // Check if user is admin
      const adminStatus = await authService.checkAdminStatus();
      if (!adminStatus.is_admin) {
        setError('You must be an administrator to use the visual editor.');
        setLoading(false);
        return;
      }
      setIsAdmin(true);
    } catch (err: any) {
      console.error('Error checking admin status:', err);
      setError(err.message || 'Failed to verify admin status');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (data: any) => {
    try {
      setSaving(true);
      setError('');

      // For now, just save to localStorage as a demo
      // In a real implementation, you would save to your backend
      localStorage.setItem('puck-editor-data', JSON.stringify(data));

      // Show success message
      alert('Design saved successfully! (Demo mode - saved to localStorage)');

      // Update the current data
      setPuckData(data);
    } catch (err: any) {
      console.error('Error saving design:', err);
      setError(err.message || 'Failed to save design');
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    navigate('/');
  };

  if (loading) {
    return (
      <Container className="py-5 text-center">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <p className="mt-3">Loading visual editor...</p>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="py-5">
        <Alert variant="danger">
          <Alert.Heading>Error</Alert.Heading>
          <p>{error}</p>
          <Button variant="outline-danger" onClick={() => navigate('/')}>
            Go Home
          </Button>
        </Alert>
      </Container>
    );
  }

  if (!isAdmin) {
    return (
      <Container className="py-5">
        <Alert variant="warning">
          <Alert.Heading>Access Denied</Alert.Heading>
          <p>You must be an administrator to use the visual editor.</p>
          <Button variant="outline-warning" onClick={() => navigate('/')}>
            Go Home
          </Button>
        </Alert>
      </Container>
    );
  }

  if (!puckData) {
    return (
      <Container className="py-5">
        <Alert variant="warning">
          <Alert.Heading>Editor Not Ready</Alert.Heading>
          <p>The visual editor is not ready yet.</p>
          <Button variant="outline-warning" onClick={() => navigate('/')}>
            Go Home
          </Button>
        </Alert>
      </Container>
    );
  }

  return (
    <div className="puck-editor-container" style={{ height: '100vh', width: '100vw', position: 'fixed', top: 0, left: 0, zIndex: 9999 }}>
      <Puck
        config={puckConfig}
        data={puckData}
        onPublish={handleSave}
        onChange={setPuckData}
        headerTitle="Visual Editor"
        renderHeaderActions={() => (
          <div className="puck-header-actions">
            <Button
              variant="primary"
              size="sm"
              onClick={() => handleSave(puckData)}
              disabled={saving}
            >
              {saving ? 'Saving...' : 'Save'}
            </Button>
            <Button
              variant="outline-secondary"
              size="sm"
              onClick={handleCancel}
              disabled={saving}
            >
              Cancel
            </Button>
          </div>
        )}
      />
      {saving && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 10000,
          }}
        >
          <div style={{ backgroundColor: 'white', padding: '2rem', borderRadius: '0.5rem' }}>
            <Spinner animation="border" className="me-2" />
            Saving design...
          </div>
        </div>
      )}
    </div>
  );
};

export default PuckEditor;
