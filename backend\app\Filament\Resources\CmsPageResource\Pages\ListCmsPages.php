<?php

namespace App\Filament\Resources\CmsPageResource\Pages;

use App\Filament\Resources\CmsPageResource;
use App\Models\CmsPage;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListCmsPages extends ListRecords
{
    protected static string $resource = CmsPageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('statistics')
                ->label('Statistics')
                ->icon('heroicon-o-chart-bar')
                ->color('info')
                ->modalContent(view('filament.pages.cms-statistics'))
                ->modalSubmitAction(false)
                ->modalCancelActionLabel('Close'),

            Actions\CreateAction::make()
                ->label('New Page')
                ->icon('heroicon-o-plus'),
        ];
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make('All Pages')
                ->badge(CmsPage::count()),

            'published' => Tab::make('Published')
                ->badge(CmsPage::where('is_published', true)->count())
                ->modifyQueryUsing(fn (Builder $query) => $query->where('is_published', true)),

            'drafts' => Tab::make('Drafts')
                ->badge(CmsPage::where('is_published', false)->count())
                ->modifyQueryUsing(fn (Builder $query) => $query->where('is_published', false)),

            'featured' => Tab::make('Featured')
                ->badge(CmsPage::where('is_featured', true)->count())
                ->modifyQueryUsing(fn (Builder $query) => $query->where('is_featured', true)),

            'html' => Tab::make('HTML Pages')
                ->badge(CmsPage::where('editor_type', 'html')->count())
                ->modifyQueryUsing(fn (Builder $query) => $query->where('editor_type', 'html')),

            'puck' => Tab::make('Puck Pages')
                ->badge(CmsPage::where('editor_type', 'puck')->count())
                ->modifyQueryUsing(fn (Builder $query) => $query->where('editor_type', 'puck')),
        ];
    }
}
