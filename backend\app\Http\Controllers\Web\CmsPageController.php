<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\CmsPage;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\Response;

class CmsPageController extends Controller
{
    /**
     * CMS Page Controller - API Only
     *
     * This controller now serves only API endpoints for the React frontend.
     * Public page display has been moved to the React frontend at localhost:3000.
     *
     * Available API endpoints:
     * - GET /pages/{slug}/content - Get page content as JSON
     * - GET /pages/{slug}/preview - Preview page (admin only)
     */

    /**
     * Get page content as JSON (for AJAX requests)
     */
    public function getPageContent(string $slug)
    {
        $page = CmsPage::published()
            ->where('slug', $slug)
            ->first();

        if (!$page) {
            return response()->json([
                'error' => 'Page not found'
            ], 404);
        }

        $content = $page->getDisplayContent();

        return response()->json([
            'success' => true,
            'data' => [
                'page' => [
                    'id' => $page->id,
                    'title' => $page->title,
                    'slug' => $page->slug,
                    'editor_type' => $page->editor_type,
                    'meta_description' => $page->meta_description,
                    'meta_keywords' => $page->meta_keywords,
                    'featured_image' => $page->featured_image,
                    'is_featured' => $page->is_featured,
                    'published_at' => $page->published_at,
                ],
                'content' => $content,
                'puck_data' => $page->isPuckPage() ? $page->puck_data : null,
            ]
        ]);
    }

    /**
     * Preview a page (for admin use, bypasses published status)
     * Returns JSON data for frontend preview
     */
    public function preview(string $slug, Request $request)
    {
        // Only allow preview for authenticated admin users
        if (!auth()->check() || !auth()->user()->isAdmin()) {
            return response()->json([
                'error' => 'Unauthorized access'
            ], 403);
        }

        $page = CmsPage::where('slug', $slug)->first();

        if (!$page) {
            return response()->json([
                'error' => 'Page not found'
            ], 404);
        }

        $content = $page->getDisplayContent();

        return response()->json([
            'success' => true,
            'preview' => true,
            'data' => [
                'page' => [
                    'id' => $page->id,
                    'title' => $page->title,
                    'slug' => $page->slug,
                    'editor_type' => $page->editor_type,
                    'meta_description' => $page->meta_description,
                    'meta_keywords' => $page->meta_keywords,
                    'featured_image' => $page->featured_image,
                    'is_featured' => $page->is_featured,
                    'is_published' => $page->is_published,
                    'published_at' => $page->published_at,
                ],
                'content' => $content,
                'puck_data' => $page->isPuckPage() ? $page->puck_data : null,
            ]
        ]);
    }
}
