<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class CmsPage extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'content',
        'puck_data',
        'editor_type',
        'rendered_content',
        'puck_meta',
        'meta_description',
        'meta_keywords',
        'meta_title',
        'meta_robots',
        'canonical_url',
        'og_title',
        'og_description',
        'og_image',
        'is_published',
        'is_featured',
        'status',
        'template',
        'featured_image',
        'sort_order',
        'page_settings',
        'created_by',
        'updated_by',
        'published_at',
        'scheduled_publish_at',
        'scheduled_unpublish_at',
    ];

    protected function casts(): array
    {
        return [
            'is_published' => 'boolean',
            'is_featured' => 'boolean',
            'published_at' => 'datetime',
            'scheduled_publish_at' => 'datetime',
            'scheduled_unpublish_at' => 'datetime',
            'puck_data' => 'array',
            'puck_meta' => 'array',
            'page_settings' => 'array',
        ];
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($page) {
            if (empty($page->slug)) {
                $page->slug = Str::slug($page->title);
            }
        });

        static::updating(function ($page) {
            if ($page->isDirty('title') && empty($page->slug)) {
                $page->slug = Str::slug($page->title);
            }
        });

        static::saving(function ($page) {
            // Auto-update rendered content when puck_data changes
            if ($page->isPuckPage() && $page->isDirty('puck_data') && $page->puck_data) {
                $page->rendered_content = $page->renderPuckContent();
            }
        });
    }

    /**
     * Get the user who created this page
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this page
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Scope for published pages
     */
    public function scopePublished($query)
    {
        return $query->where(function ($q) {
            $q->where('is_published', true)
              ->orWhere('status', 'published');
        })
        ->whereNotNull('published_at')
        ->where('published_at', '<=', now())
        ->where(function ($q) {
            $q->whereNull('scheduled_unpublish_at')
              ->orWhere('scheduled_unpublish_at', '>', now());
        });
    }

    /**
     * Scope for featured pages
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope for pages using Puck editor
     */
    public function scopePuckPages($query)
    {
        return $query->where('editor_type', 'puck');
    }

    /**
     * Scope for pages using HTML editor
     */
    public function scopeHtmlPages($query)
    {
        return $query->where('editor_type', 'html');
    }

    /**
     * Scope for pages by status
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for draft pages
     */
    public function scopeDrafts($query)
    {
        return $query->where('status', 'draft');
    }

    /**
     * Scope for archived pages
     */
    public function scopeArchived($query)
    {
        return $query->where('status', 'archived');
    }

    /**
     * Scope for pages by template
     */
    public function scopeByTemplate($query, string $template)
    {
        return $query->where('template', $template);
    }

    /**
     * Scope for scheduled pages
     */
    public function scopeScheduled($query)
    {
        return $query->whereNotNull('scheduled_publish_at')
                    ->where('scheduled_publish_at', '>', now());
    }

    /**
     * Scope for pages ordered by sort order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')
                    ->orderBy('created_at', 'desc');
    }

    /**
     * Check if this page uses Puck editor
     */
    public function isPuckPage(): bool
    {
        return $this->editor_type === 'puck';
    }

    /**
     * Check if this page uses HTML editor
     */
    public function isHtmlPage(): bool
    {
        return $this->editor_type === 'html';
    }

    /**
     * Check if page is published
     */
    public function isPublished(): bool
    {
        return $this->status === 'published' ||
               ($this->is_published &&
                $this->published_at &&
                $this->published_at <= now() &&
                (!$this->scheduled_unpublish_at || $this->scheduled_unpublish_at > now()));
    }

    /**
     * Check if page is draft
     */
    public function isDraft(): bool
    {
        return $this->status === 'draft';
    }

    /**
     * Check if page is archived
     */
    public function isArchived(): bool
    {
        return $this->status === 'archived';
    }

    /**
     * Check if page is scheduled for publishing
     */
    public function isScheduled(): bool
    {
        return $this->scheduled_publish_at && $this->scheduled_publish_at > now();
    }

    /**
     * Get the effective meta title (meta_title or title)
     */
    public function getEffectiveMetaTitle(): string
    {
        return $this->meta_title ?: $this->title;
    }

    /**
     * Get the effective meta description
     */
    public function getEffectiveMetaDescription(): string
    {
        return $this->meta_description ?: '';
    }

    /**
     * Get Open Graph title (og_title or meta_title or title)
     */
    public function getOgTitle(): string
    {
        return $this->og_title ?: $this->getEffectiveMetaTitle();
    }

    /**
     * Get Open Graph description (og_description or meta_description)
     */
    public function getOgDescription(): string
    {
        return $this->og_description ?: $this->getEffectiveMetaDescription();
    }

    /**
     * Get Open Graph image (og_image or featured_image)
     */
    public function getOgImage(): ?string
    {
        return $this->og_image ?: $this->featured_image;
    }

    /**
     * Get the display content based on editor type
     */
    public function getDisplayContent(): string
    {
        if ($this->isPuckPage()) {
            return $this->rendered_content ?: $this->renderPuckContent();
        }

        return $this->content ?: '';
    }

    /**
     * Render Puck data to HTML for public display
     */
    public function renderPuckContent(): string
    {
        if (!$this->puck_data) {
            return '';
        }

        // This would integrate with your Puck renderer
        // For now, return a basic HTML structure
        $html = '';

        if (isset($this->puck_data['content']) && is_array($this->puck_data['content'])) {
            foreach ($this->puck_data['content'] as $component) {
                $html .= $this->renderPuckComponent($component);
            }
        }

        return $html;
    }

    /**
     * Render a single Puck component to HTML
     */
    private function renderPuckComponent(array $component): string
    {
        $type = $component['type'] ?? '';
        $props = $component['props'] ?? [];

        switch ($type) {
            case 'Hero':
                return $this->renderHeroComponent($props);
            case 'Text':
                return $this->renderTextComponent($props);
            case 'Heading':
                return $this->renderHeadingComponent($props);
            case 'Button':
                return $this->renderButtonComponent($props);
            case 'Image':
                return $this->renderImageComponent($props);
            case 'Container':
                return $this->renderContainerComponent($props);
            case 'Spacer':
                return $this->renderSpacerComponent($props);
            case 'Divider':
                return $this->renderDividerComponent($props);
            case 'Card':
                return $this->renderCardComponent($props);
            case 'Columns':
                return $this->renderColumnsComponent($props);
            case 'List':
                return $this->renderListComponent($props);
            default:
                return "<!-- Unknown component: {$type} -->";
        }
    }

    /**
     * Render Hero component
     */
    private function renderHeroComponent(array $props): string
    {
        $title = $props['title'] ?? '';
        $subtitle = $props['subtitle'] ?? '';
        $backgroundImage = $props['backgroundImage'] ?? '';
        $textAlign = $props['textAlign'] ?? 'center';
        $minHeight = $props['minHeight'] ?? '400px';

        $style = "min-height: {$minHeight}; text-align: {$textAlign};";
        if ($backgroundImage) {
            $style .= " background-image: url('{$backgroundImage}'); background-size: cover; background-position: center;";
        }

        $html = "<section class=\"hero\" style=\"{$style}\">";
        if ($title) {
            $html .= "<h1>{$title}</h1>";
        }
        if ($subtitle) {
            $html .= "<p>{$subtitle}</p>";
        }

        // Add buttons if they exist
        if (isset($props['primaryButton'])) {
            $button = $props['primaryButton'];
            $html .= "<a href=\"{$button['href']}\" class=\"btn btn-{$button['variant']}\">{$button['text']}</a>";
        }

        $html .= "</section>";

        return $html;
    }

    /**
     * Render Text component
     */
    private function renderTextComponent(array $props): string
    {
        $content = $props['content'] ?? '<p>Enter your text content here...</p>';
        $textAlign = $props['textAlign'] ?? 'left';
        $fontSize = $props['fontSize'] ?? '16px';
        $color = $props['color'] ?? '#333333';
        $lineHeight = $props['lineHeight'] ?? '1.6';
        $marginTop = $props['marginTop'] ?? '0px';
        $marginBottom = $props['marginBottom'] ?? '20px';

        $styles = [
            'text-align: ' . $textAlign,
            'font-size: ' . $fontSize,
            'color: ' . $color,
            'line-height: ' . $lineHeight,
            'margin-top: ' . $marginTop,
            'margin-bottom: ' . $marginBottom,
            'padding: 0 1rem',
        ];

        return '<div class="text-component" style="' . implode('; ', $styles) . '">' . $content . '</div>';
    }

    private function renderHeadingComponent(array $props): string
    {
        $text = htmlspecialchars($props['text'] ?? 'Heading Text');
        $level = $props['level'] ?? 'h2';
        $textAlign = $props['textAlign'] ?? 'left';
        $color = $props['color'] ?? '#333333';
        $marginTop = $props['marginTop'] ?? '0px';
        $marginBottom = $props['marginBottom'] ?? '20px';

        $fontSizes = [
            'h1' => '2.5rem',
            'h2' => '2rem',
            'h3' => '1.75rem',
            'h4' => '1.5rem',
            'h5' => '1.25rem',
            'h6' => '1rem',
        ];
        $fontSize = $fontSizes[$level] ?? '2rem';

        $styles = [
            'text-align: ' . $textAlign,
            'color: ' . $color,
            'font-size: ' . $fontSize,
            'font-weight: bold',
            'margin-top: ' . $marginTop,
            'margin-bottom: ' . $marginBottom,
            'padding: 0 1rem',
            'line-height: 1.2',
        ];

        return '<' . $level . ' style="' . implode('; ', $styles) . '">' . $text . '</' . $level . '>';
    }

    private function renderButtonComponent(array $props): string
    {
        $text = htmlspecialchars($props['text'] ?? 'Button Text');
        $href = htmlspecialchars($props['href'] ?? '#');
        $variant = $props['variant'] ?? 'primary';
        $size = $props['size'] ?? 'md';
        $textAlign = $props['textAlign'] ?? 'center';
        $fullWidth = $props['fullWidth'] ?? false;
        $target = $props['target'] ?? '_self';

        $colors = [
            'primary' => '#007bff',
            'secondary' => '#6c757d',
            'success' => '#28a745',
            'danger' => '#dc3545',
            'warning' => '#ffc107',
            'info' => '#17a2b8',
            'light' => '#f8f9fa',
            'dark' => '#343a40',
        ];

        $textColors = [
            'primary' => 'white',
            'secondary' => 'white',
            'success' => 'white',
            'danger' => 'white',
            'warning' => '#212529',
            'info' => 'white',
            'light' => '#212529',
            'dark' => 'white',
        ];

        $padding = $size === 'sm' ? '0.5rem 1rem' : ($size === 'lg' ? '1rem 2rem' : '0.75rem 1.5rem');
        $fontSize = $size === 'sm' ? '0.875rem' : ($size === 'lg' ? '1.125rem' : '1rem');

        $buttonStyles = [
            'display: ' . ($fullWidth ? 'block' : 'inline-block'),
            'width: ' . ($fullWidth ? '100%' : 'auto'),
            'padding: ' . $padding,
            'background-color: ' . ($colors[$variant] ?? $colors['primary']),
            'color: ' . ($textColors[$variant] ?? 'white'),
            'text-decoration: none',
            'border-radius: 0.375rem',
            'font-weight: 500',
            'font-size: ' . $fontSize,
            'text-align: center',
            'border: none',
            'cursor: pointer',
        ];

        $containerStyles = [
            'padding: 1rem',
            'text-align: ' . $textAlign,
        ];

        $targetAttr = $target === '_blank' ? ' target="_blank" rel="noopener noreferrer"' : '';

        return '<div style="' . implode('; ', $containerStyles) . '">' .
               '<a href="' . $href . '"' . $targetAttr . ' style="' . implode('; ', $buttonStyles) . '">' .
               $text . '</a></div>';
    }

    /**
     * Render Container component
     */
    private function renderContainerComponent(array $props): string
    {
        $maxWidth = $props['maxWidth'] ?? '1200px';
        $padding = $props['padding'] ?? '20px';
        $backgroundColor = $props['backgroundColor'] ?? '';

        $style = "max-width: {$maxWidth}; padding: {$padding}; margin: 0 auto;";
        if ($backgroundColor) {
            $style .= " background-color: {$backgroundColor};";
        }

        return "<div class=\"container-component\" style=\"{$style}\"></div>";
    }

    /**
     * Render Image component
     */
    private function renderImageComponent(array $props): string
    {
        $src = $props['src'] ?? '';
        $alt = $props['alt'] ?? '';
        $width = $props['width'] ?? '';
        $height = $props['height'] ?? '';
        $objectFit = $props['objectFit'] ?? 'cover';

        if (!$src) {
            return '';
        }

        $style = "object-fit: {$objectFit};";
        if ($width) {
            $style .= " width: {$width};";
        }
        if ($height) {
            $style .= " height: {$height};";
        }

        return "<img src=\"{$src}\" alt=\"{$alt}\" style=\"{$style}\" />";
    }

    private function renderSpacerComponent(array $props): string
    {
        $height = $props['height'] ?? '40px';
        return '<div style="height: ' . $height . '; width: 100%;"></div>';
    }

    private function renderDividerComponent(array $props): string
    {
        $color = $props['color'] ?? '#e0e0e0';
        $thickness = $props['thickness'] ?? '1px';
        $style = $props['style'] ?? 'solid';
        $marginTop = $props['marginTop'] ?? '20px';
        $marginBottom = $props['marginBottom'] ?? '20px';

        $containerStyles = [
            'padding: 0 1rem',
            'margin-top: ' . $marginTop,
            'margin-bottom: ' . $marginBottom,
        ];

        $hrStyles = [
            'border: none',
            'border-top: ' . $thickness . ' ' . $style . ' ' . $color,
            'margin: 0',
        ];

        return '<div style="' . implode('; ', $containerStyles) . '">' .
               '<hr style="' . implode('; ', $hrStyles) . '" /></div>';
    }

    private function renderCardComponent(array $props): string
    {
        $title = htmlspecialchars($props['title'] ?? 'Card Title');
        $content = $props['content'] ?? '<p>Card content goes here...</p>';
        $imageUrl = htmlspecialchars($props['imageUrl'] ?? '');
        $backgroundColor = $props['backgroundColor'] ?? '#ffffff';
        $borderColor = $props['borderColor'] ?? '#e0e0e0';
        $borderRadius = $props['borderRadius'] ?? '8px';
        $padding = $props['padding'] ?? '1.5rem';
        $shadow = $props['shadow'] ?? true;

        $containerStyles = ['padding: 1rem'];

        $cardStyles = [
            'background-color: ' . $backgroundColor,
            'border: 1px solid ' . $borderColor,
            'border-radius: ' . $borderRadius,
            'padding: ' . $padding,
            'overflow: hidden',
        ];

        if ($shadow) {
            $cardStyles[] = 'box-shadow: 0 2px 4px rgba(0,0,0,0.1)';
        }

        $html = '<div style="' . implode('; ', $containerStyles) . '">';
        $html .= '<div style="' . implode('; ', $cardStyles) . '">';

        if ($imageUrl) {
            $html .= '<img src="' . $imageUrl . '" alt="' . $title . '" style="width: 100%; height: 200px; object-fit: cover; margin-bottom: 1rem; border-radius: 4px;" />';
        }

        if ($title) {
            $html .= '<h3 style="margin: 0 0 1rem 0; font-size: 1.25rem; font-weight: bold; color: #333;">' . $title . '</h3>';
        }

        $html .= $content;
        $html .= '</div></div>';

        return $html;
    }

    private function renderColumnsComponent(array $props): string
    {
        $columns = $props['columns'] ?? 2;
        $gap = $props['gap'] ?? '2rem';
        $alignItems = $props['alignItems'] ?? 'stretch';

        $containerStyles = ['padding: 1rem'];

        $gridStyles = [
            'display: grid',
            'grid-template-columns: repeat(' . $columns . ', 1fr)',
            'gap: ' . $gap,
            'align-items: ' . $alignItems,
        ];

        return '<div style="' . implode('; ', $containerStyles) . '">' .
               '<div style="' . implode('; ', $gridStyles) . '"></div></div>';
    }

    private function renderListComponent(array $props): string
    {
        $items = $props['items'] ?? ['List item 1', 'List item 2', 'List item 3'];
        $listType = $props['listType'] ?? 'ul';
        $color = $props['color'] ?? '#333333';
        $fontSize = $props['fontSize'] ?? '16px';

        if (!is_array($items)) {
            $items = ['List item 1', 'List item 2', 'List item 3'];
        }

        $containerStyles = ['padding: 1rem'];

        $listStyles = [
            'color: ' . $color,
            'font-size: ' . $fontSize,
            'line-height: 1.6',
            'padding-left: 1.5rem',
        ];

        $listTag = $listType === 'ol' ? 'ol' : 'ul';

        $html = '<div style="' . implode('; ', $containerStyles) . '">';
        $html .= '<' . $listTag . ' style="' . implode('; ', $listStyles) . '">';

        foreach ($items as $item) {
            $html .= '<li style="margin-bottom: 0.5rem;">' . htmlspecialchars($item) . '</li>';
        }

        $html .= '</' . $listTag . '></div>';

        return $html;
    }

    /**
     * Convert HTML content to basic Puck structure
     */
    public function convertHtmlToPuck(): array
    {
        if ($this->isPuckPage() || !$this->content) {
            return $this->puck_data ?: [];
        }

        // Basic conversion - create a single Text component with the HTML content
        return [
            'content' => [
                [
                    'type' => 'Text',
                    'props' => [
                        'id' => 'text-' . uniqid(),
                        'content' => $this->content,
                        'textAlign' => 'left',
                    ]
                ]
            ],
            'root' => [
                'title' => $this->title,
                'metaDescription' => $this->meta_description,
                'metaKeywords' => $this->meta_keywords,
            ]
        ];
    }

    /**
     * Update rendered content from Puck data
     */
    public function updateRenderedContent(): void
    {
        if ($this->isPuckPage() && $this->puck_data) {
            $this->rendered_content = $this->renderPuckContent();
            $this->save();
        }
    }



    /**
     * Switch editor type and convert content
     */
    public function switchEditorType(string $newType): bool
    {
        if (!in_array($newType, ['html', 'puck'])) {
            return false;
        }

        if ($this->editor_type === $newType) {
            return true; // Already using this editor type
        }

        if ($newType === 'puck') {
            // Convert HTML to Puck
            $this->puck_data = $this->convertHtmlToPuck();
            $this->rendered_content = $this->content; // Keep original HTML as rendered content
        } else {
            // Convert Puck to HTML
            $this->content = $this->rendered_content ?: $this->renderPuckContent();
            $this->puck_data = null;
            $this->rendered_content = null;
        }

        $this->editor_type = $newType;
        return $this->save();
    }
}
