<?php

namespace App\Providers;

use App\Models\CmsPage;
use App\Policies\CmsPagePolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        CmsPage::class => CmsPagePolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();

        // Additional gates can be defined here
        Gate::define('manage-cms', function ($user) {
            return $user->role === 'admin';
        });

        Gate::define('publish-cms-pages', function ($user) {
            return $user->role === 'admin';
        });

        Gate::define('feature-cms-pages', function ($user) {
            return $user->role === 'admin';
        });
    }
}
