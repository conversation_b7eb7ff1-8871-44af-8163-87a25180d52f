<?php

namespace App\Http\Requests\Cms;

use App\Http\Requests\BaseRequest;
use App\Models\CmsPage;

class CreateCmsPageRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only admin users can create CMS pages
        return auth()->check() && auth()->user()->isAdmin();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $commonRules = $this->getCommonRules();
        
        return [
            'title' => $commonRules['name_rule'],
            'slug' => 'nullable|string|max:255|unique:cms_pages,slug|regex:/^[a-z0-9\-]+$/',
            'content' => 'nullable|string|max:100000',
            'editor_type' => 'required|in:html,puck',
            'puck_data' => 'nullable|array',
            'template' => 'required|string|max:50|in:default,landing,blog,minimal',
            'status' => 'required|in:draft,published,archived',
            
            // SEO fields
            'meta_title' => 'nullable|string|max:60',
            'meta_description' => 'nullable|string|max:160',
            'meta_keywords' => 'nullable|string|max:255',
            'meta_robots' => 'nullable|string|max:100',
            'canonical_url' => 'nullable|url|max:255',
            
            // Open Graph fields
            'og_title' => 'nullable|string|max:60',
            'og_description' => 'nullable|string|max:160',
            'og_image' => 'nullable|url|max:255',
            
            // Publishing fields
            'is_published' => 'boolean',
            'is_featured' => 'boolean',
            'featured_image' => 'nullable|url|max:255',
            'published_at' => 'nullable|date',
            'scheduled_publish_at' => 'nullable|date|after:now',
            'scheduled_unpublish_at' => 'nullable|date|after:scheduled_publish_at',
            
            // Content management
            'sort_order' => 'integer|min:0|max:9999',
            'page_settings' => 'nullable|array',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return array_merge(parent::messages(), [
            'title.required' => 'Page title is required.',
            'slug.unique' => 'This URL slug is already in use. Please choose a different one.',
            'slug.regex' => 'URL slug can only contain lowercase letters, numbers, and hyphens.',
            'editor_type.required' => 'Please select an editor type.',
            'editor_type.in' => 'Editor type must be either HTML or Puck.',
            'template.required' => 'Please select a page template.',
            'template.in' => 'Invalid template selected.',
            'status.required' => 'Please select a page status.',
            'status.in' => 'Page status must be draft, published, or archived.',
            'meta_title.max' => 'Meta title should not exceed 60 characters for optimal SEO.',
            'meta_description.max' => 'Meta description should not exceed 160 characters for optimal SEO.',
            'canonical_url.url' => 'Canonical URL must be a valid URL.',
            'og_title.max' => 'Open Graph title should not exceed 60 characters.',
            'og_description.max' => 'Open Graph description should not exceed 160 characters.',
            'og_image.url' => 'Open Graph image must be a valid URL.',
            'featured_image.url' => 'Featured image must be a valid URL.',
            'scheduled_publish_at.after' => 'Scheduled publish date must be in the future.',
            'scheduled_unpublish_at.after' => 'Scheduled unpublish date must be after the publish date.',
            'sort_order.min' => 'Sort order must be a positive number.',
            'sort_order.max' => 'Sort order cannot exceed 9999.',
            'content.max' => 'Content is too long. Please reduce the content size.',
        ]);
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return array_merge(parent::attributes(), [
            'meta_title' => 'SEO title',
            'meta_description' => 'SEO description',
            'meta_keywords' => 'SEO keywords',
            'meta_robots' => 'robots directive',
            'canonical_url' => 'canonical URL',
            'og_title' => 'Open Graph title',
            'og_description' => 'Open Graph description',
            'og_image' => 'Open Graph image',
            'is_published' => 'published status',
            'is_featured' => 'featured status',
            'featured_image' => 'featured image',
            'published_at' => 'publish date',
            'scheduled_publish_at' => 'scheduled publish date',
            'scheduled_unpublish_at' => 'scheduled unpublish date',
            'sort_order' => 'sort order',
            'page_settings' => 'page settings',
        ]);
    }

    /**
     * Fields that are allowed to contain HTML
     */
    protected function getAllowedHtmlFields(): array
    {
        return [
            'content',
            'meta_description',
            'og_description',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        parent::prepareForValidation();

        // Auto-generate slug if not provided
        if (empty($this->slug) && !empty($this->title)) {
            $this->merge([
                'slug' => \Illuminate\Support\Str::slug($this->title)
            ]);
        }

        // Set default values
        $this->merge([
            'template' => $this->template ?? 'default',
            'status' => $this->status ?? 'draft',
            'sort_order' => $this->sort_order ?? 0,
            'is_published' => $this->boolean('is_published'),
            'is_featured' => $this->boolean('is_featured'),
        ]);

        // Handle editor type specific validation
        if ($this->editor_type === 'puck') {
            // For Puck editor, content should be null
            $this->merge(['content' => null]);
        } else {
            // For HTML editor, puck_data should be null
            $this->merge(['puck_data' => null]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Additional validation for Puck data
            if ($this->editor_type === 'puck' && $this->puck_data) {
                if (!is_array($this->puck_data)) {
                    $validator->errors()->add('puck_data', 'Puck data must be a valid JSON object.');
                }
            }

            // Validate scheduling logic
            if ($this->scheduled_publish_at && $this->scheduled_unpublish_at) {
                if (strtotime($this->scheduled_unpublish_at) <= strtotime($this->scheduled_publish_at)) {
                    $validator->errors()->add('scheduled_unpublish_at', 'Unpublish date must be after publish date.');
                }
            }

            // Validate slug uniqueness with better error message
            if ($this->slug) {
                $exists = CmsPage::where('slug', $this->slug)->exists();
                if ($exists) {
                    $validator->errors()->add('slug', 'A page with this URL slug already exists.');
                }
            }
        });
    }
}
