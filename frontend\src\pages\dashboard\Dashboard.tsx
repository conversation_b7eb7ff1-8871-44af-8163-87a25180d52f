import React from 'react';
import {
  Container,
  Row,
  Col,
  Card,
} from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { DashboardCharts } from '../../components/dashboard/ChartExample';
import dattaAbleTheme from '../../theme/dattaAbleTheme';

interface StatCardProps {
  title: string;
  value: string;
  icon: string;
  color: string;
  change?: string;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, color, change }) => (
  <Card 
    className="h-100 border-0 shadow-sm"
    style={{ 
      borderRadius: dattaAbleTheme.borderRadius.lg,
      transition: 'all 0.3s ease',
    }}
  >
    <Card.Body className="p-4">
      <div className="d-flex align-items-center justify-content-between">
        <div>
          <small className="text-muted text-uppercase fw-semibold d-block mb-2">
            {title}
          </small>
          <h3 className="fw-bold mb-1">{value}</h3>
          {change && (
            <small className="text-success fw-semibold">
              <i className="fas fa-arrow-up me-1"></i>
              {change}
            </small>
          )}
        </div>
        <div 
          className="d-flex align-items-center justify-content-center"
          style={{
            width: '56px',
            height: '56px',
            borderRadius: '50%',
            backgroundColor: color,
            color: 'white',
          }}
        >
          <i className={icon} style={{ fontSize: '1.5rem' }}></i>
        </div>
      </div>
    </Card.Body>
  </Card>
);

const Dashboard: React.FC = () => {
  const stats = [
    {
      title: 'Total Revenue',
      value: '$12,426',
      icon: 'fas fa-dollar-sign',
      color: dattaAbleTheme.colors.success.main,
      change: '+12.5%',
    },
    {
      title: 'Total Users',
      value: '1,426',
      icon: 'fas fa-users',
      color: dattaAbleTheme.colors.primary.main,
      change: '+8.2%',
    },
    {
      title: 'Total Orders',
      value: '324',
      icon: 'fas fa-shopping-cart',
      color: dattaAbleTheme.colors.warning.main,
      change: '+5.1%',
    },
    {
      title: 'Growth Rate',
      value: '23.5%',
      icon: 'fas fa-chart-line',
      color: dattaAbleTheme.colors.info.main,
      change: '+2.3%',
    },
  ];

  return (
    <Container fluid className="px-0">
      {/* Welcome Section */}
      <Row className="mb-4">
        <Col>
          <div 
            className="p-4 text-white position-relative overflow-hidden"
            style={{
              background: `linear-gradient(135deg, ${dattaAbleTheme.colors.primary.main} 0%, ${dattaAbleTheme.colors.primary.dark} 100%)`,
              borderRadius: dattaAbleTheme.borderRadius.lg,
              boxShadow: dattaAbleTheme.shadows.lg,
            }}
          >
            <div className="position-relative" style={{ zIndex: 1 }}>
              <h2 className="fw-bold mb-2">Welcome back!</h2>
              <p className="mb-0 opacity-75">
                Here's what's happening with your dashboard today.
              </p>
            </div>
            {/* Background decoration */}
            <div 
              className="position-absolute"
              style={{
                top: 0,
                right: 0,
                width: '40%',
                height: '100%',
                background: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="4"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat',
                opacity: 0.1,
              }}
            ></div>
          </div>
        </Col>
      </Row>

      {/* Statistics Cards */}
      <Row className="g-4 mb-4">
        {stats.map((stat, index) => (
          <Col key={index} xs={12} sm={6} lg={3}>
            <StatCard {...stat} />
          </Col>
        ))}
      </Row>

      {/* Charts Section */}
      <Row className="g-4">
        <Col lg={8}>
          <Card 
            className="border-0 shadow-sm h-100"
            style={{ borderRadius: dattaAbleTheme.borderRadius.lg }}
          >
            <Card.Header className="bg-transparent border-0 pb-0">
              <div className="d-flex align-items-center justify-content-between">
                <div>
                  <h5 className="fw-semibold mb-1">Analytics Overview</h5>
                  <small className="text-muted">Monthly performance metrics</small>
                </div>
                <div className="d-flex gap-2">
                  <span className="badge bg-primary">This Month</span>
                </div>
              </div>
            </Card.Header>
            <Card.Body>
              <DashboardCharts />
            </Card.Body>
          </Card>
        </Col>

        <Col lg={4}>
          <Row className="g-4">
            {/* Recent Activity */}
            <Col xs={12}>
              <Card 
                className="border-0 shadow-sm"
                style={{ borderRadius: dattaAbleTheme.borderRadius.lg }}
              >
                <Card.Header className="bg-transparent border-0 pb-0">
                  <h6 className="fw-semibold mb-0">Recent Activity</h6>
                </Card.Header>
                <Card.Body>
                  <div className="d-flex flex-column gap-3">
                    <div className="d-flex align-items-start gap-3">
                      <div 
                        className="d-flex align-items-center justify-content-center flex-shrink-0"
                        style={{
                          width: '32px',
                          height: '32px',
                          borderRadius: '50%',
                          backgroundColor: `${dattaAbleTheme.colors.success.main}20`,
                          color: dattaAbleTheme.colors.success.main,
                        }}
                      >
                        <i className="fas fa-plus" style={{ fontSize: '0.75rem' }}></i>
                      </div>
                      <div className="flex-grow-1">
                        <h6 className="mb-1 fw-semibold">New Order Received</h6>
                        <small className="text-muted">Order #12345 - $125.00</small>
                        <div>
                          <small className="text-muted">2 minutes ago</small>
                        </div>
                      </div>
                    </div>

                    <div className="d-flex align-items-start gap-3">
                      <div 
                        className="d-flex align-items-center justify-content-center flex-shrink-0"
                        style={{
                          width: '32px',
                          height: '32px',
                          borderRadius: '50%',
                          backgroundColor: `${dattaAbleTheme.colors.primary.main}20`,
                          color: dattaAbleTheme.colors.primary.main,
                        }}
                      >
                        <i className="fas fa-user" style={{ fontSize: '0.75rem' }}></i>
                      </div>
                      <div className="flex-grow-1">
                        <h6 className="mb-1 fw-semibold">New User Registration</h6>
                        <small className="text-muted"><EMAIL></small>
                        <div>
                          <small className="text-muted">5 minutes ago</small>
                        </div>
                      </div>
                    </div>

                    <div className="d-flex align-items-start gap-3">
                      <div 
                        className="d-flex align-items-center justify-content-center flex-shrink-0"
                        style={{
                          width: '32px',
                          height: '32px',
                          borderRadius: '50%',
                          backgroundColor: `${dattaAbleTheme.colors.warning.main}20`,
                          color: dattaAbleTheme.colors.warning.main,
                        }}
                      >
                        <i className="fas fa-wallet" style={{ fontSize: '0.75rem' }}></i>
                      </div>
                      <div className="flex-grow-1">
                        <h6 className="mb-1 fw-semibold">Wallet Top-up</h6>
                        <small className="text-muted">+RM 100.00</small>
                        <div>
                          <small className="text-muted">10 minutes ago</small>
                        </div>
                      </div>
                    </div>
                  </div>
                </Card.Body>
              </Card>
            </Col>

            {/* Quick Actions */}
            <Col xs={12}>
              <Card 
                className="border-0 shadow-sm"
                style={{ borderRadius: dattaAbleTheme.borderRadius.lg }}
              >
                <Card.Header className="bg-transparent border-0 pb-0">
                  <h6 className="fw-semibold mb-0">Quick Actions</h6>
                </Card.Header>
                <Card.Body>
                  <div className="d-grid gap-2">
                    <button 
                      className="btn btn-outline-primary d-flex align-items-center justify-content-center gap-2"
                      style={{ borderRadius: dattaAbleTheme.borderRadius.md }}
                    >
                      <i className="fas fa-plus"></i>
                      Create New Order
                    </button>
                    <button 
                      className="btn btn-outline-success d-flex align-items-center justify-content-center gap-2"
                      style={{ borderRadius: dattaAbleTheme.borderRadius.md }}
                    >
                      <i className="fas fa-wallet"></i>
                      Top Up Wallet
                    </button>
                    <button
                      className="btn btn-outline-info d-flex align-items-center justify-content-center gap-2"
                      style={{ borderRadius: dattaAbleTheme.borderRadius.md }}
                    >
                      <i className="fas fa-chart-bar"></i>
                      View Reports
                    </button>
                    {user?.role === 'admin' && (
                      <Link
                        to="/visual-editor"
                        className="btn btn-outline-warning d-flex align-items-center justify-content-center gap-2 text-decoration-none"
                        style={{ borderRadius: dattaAbleTheme.borderRadius.md }}
                      >
                        <i className="fas fa-paint-brush"></i>
                        Visual Editor
                      </Link>
                    )}
                  </div>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Col>
      </Row>
    </Container>
  );
};

export default Dashboard;
