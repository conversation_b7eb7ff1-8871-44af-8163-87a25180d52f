<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CmsPageResource\Pages;
use App\Filament\Resources\CmsPageResource\RelationManagers;
use App\Models\CmsPage;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Notifications\Notification;
use Filament\Support\Enums\FontWeight;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Database\Eloquent\Collection;

class CmsPageResource extends Resource
{
    protected static ?string $model = CmsPage::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationGroup = 'Content Management';

    protected static ?string $modelLabel = 'CMS Page';

    protected static ?string $pluralModelLabel = 'CMS Pages';

    protected static ?int $navigationSort = 1;

    protected static ?string $recordTitleAttribute = 'title';

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->check() && auth()->user()->can('viewAny', static::getModel());
    }

    public static function canAccess(): bool
    {
        return auth()->check() && auth()->user()->can('viewAny', static::getModel());
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }

    public static function getNavigationBadgeColor(): string|array|null
    {
        return static::getModel()::count() > 10 ? 'warning' : 'primary';
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->description('Enter the basic page information and content')
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->required()
                            ->maxLength(255)
                            ->live(onBlur: true)
                            ->afterStateUpdated(function (string $context, $state, Forms\Set $set) {
                                if ($context !== 'create') {
                                    return;
                                }
                                $set('slug', \Illuminate\Support\Str::slug($state));
                            })
                            ->helperText('The page title that will be displayed to users'),

                        Forms\Components\TextInput::make('slug')
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true)
                            ->rules(['alpha_dash'])
                            ->helperText('URL-friendly version of the title (auto-generated)'),

                        Forms\Components\Select::make('template')
                            ->required()
                            ->options([
                                'default' => 'Default',
                                'landing' => 'Landing Page',
                                'blog' => 'Blog Post',
                                'minimal' => 'Minimal',
                            ])
                            ->default('default')
                            ->helperText('Choose the page template/layout'),

                        Forms\Components\Select::make('status')
                            ->required()
                            ->options([
                                'draft' => 'Draft',
                                'published' => 'Published',
                                'archived' => 'Archived',
                            ])
                            ->default('draft')
                            ->helperText('Page publication status'),

                        Forms\Components\Select::make('editor_type')
                            ->label('Editor Type')
                            ->options([
                                'html' => 'HTML Editor',
                                'puck' => 'Puck Visual Editor',
                            ])
                            ->default('html')
                            ->required()
                            ->live()
                            ->afterStateUpdated(function ($state, $set, $get) {
                                // Clear content when switching editor types
                                if ($state === 'puck') {
                                    $set('content', '');
                                } else {
                                    $set('puck_data', null);
                                    $set('rendered_content', '');
                                }
                            })
                            ->helperText('Choose the editor type for this page')
                            ->columnSpanFull(),
                    ])->columns(2),

                Forms\Components\Section::make('Content')
                    ->description('Create and edit your page content')
                    ->schema([
                        Forms\Components\RichEditor::make('content')
                            ->required(fn ($get) => $get('editor_type') === 'html')
                            ->visible(fn ($get) => $get('editor_type') === 'html')
                            ->columnSpanFull()
                            ->dehydrated(fn ($get) => $get('editor_type') === 'html')
                            ->toolbarButtons([
                                'attachFiles',
                                'blockquote',
                                'bold',
                                'bulletList',
                                'codeBlock',
                                'h2',
                                'h3',
                                'italic',
                                'link',
                                'orderedList',
                                'redo',
                                'strike',
                                'underline',
                                'undo',
                            ]),

                        Forms\Components\Textarea::make('puck_data')
                            ->label('Puck Data (JSON)')
                            ->visible(fn ($get) => $get('editor_type') === 'puck')
                            ->dehydrated(fn ($get) => $get('editor_type') === 'puck')
                            ->rows(10)
                            ->columnSpanFull()
                            ->helperText('This field contains the Puck visual editor data in JSON format. Use the Puck editor interface to modify content.')
                            ->formatStateUsing(fn ($state) => is_array($state) ? json_encode($state, JSON_PRETTY_PRINT) : $state)
                            ->dehydrateStateUsing(fn ($state) => $state ? json_decode($state, true) : null),

                        Forms\Components\Textarea::make('rendered_content')
                            ->label('Rendered HTML (Preview)')
                            ->visible(fn ($get) => $get('editor_type') === 'puck')
                            ->dehydrated(fn ($get) => $get('editor_type') === 'puck')
                            ->rows(5)
                            ->disabled()
                            ->columnSpanFull()
                            ->helperText('This shows the HTML output generated from the Puck data.'),
                    ]),

                Forms\Components\Section::make('SEO & Meta Information')
                    ->description('Optimize your page for search engines and social media')
                    ->schema([
                        Forms\Components\Textarea::make('meta_description')
                            ->label('Meta Description')
                            ->maxLength(160)
                            ->rows(3)
                            ->helperText('Brief description for search engines (max 160 characters)')
                            ->live()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                $length = strlen($state ?? '');
                                if ($length > 160) {
                                    Notification::make()
                                        ->warning()
                                        ->title('Meta description is too long')
                                        ->body("Current length: {$length} characters. Recommended: 160 characters or less.")
                                        ->send();
                                }
                            }),

                        Forms\Components\TextInput::make('meta_keywords')
                            ->label('Meta Keywords')
                            ->maxLength(255)
                            ->helperText('Comma-separated keywords for SEO'),

                        Forms\Components\TextInput::make('meta_title')
                            ->label('SEO Title')
                            ->maxLength(60)
                            ->helperText('SEO title (max 60 characters, leave empty to use page title)')
                            ->live()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                $length = strlen($state ?? '');
                                if ($length > 60) {
                                    Notification::make()
                                        ->warning()
                                        ->title('SEO title is too long')
                                        ->body("Current length: {$length} characters. Recommended: 60 characters or less.")
                                        ->send();
                                }
                            }),

                        Forms\Components\TextInput::make('meta_robots')
                            ->label('Robots Directive')
                            ->maxLength(100)
                            ->placeholder('index, follow')
                            ->helperText('Robots directive (e.g., index, follow, noindex, nofollow)'),

                        Forms\Components\TextInput::make('canonical_url')
                            ->label('Canonical URL')
                            ->url()
                            ->maxLength(255)
                            ->helperText('Canonical URL for this page (optional)'),

                        Forms\Components\TextInput::make('og_title')
                            ->label('Open Graph Title')
                            ->maxLength(60)
                            ->helperText('Title for social media sharing (leave empty to use SEO title)'),

                        Forms\Components\Textarea::make('og_description')
                            ->label('Open Graph Description')
                            ->maxLength(160)
                            ->rows(3)
                            ->helperText('Description for social media sharing (leave empty to use meta description)'),

                        Forms\Components\TextInput::make('og_image')
                            ->label('Open Graph Image')
                            ->url()
                            ->maxLength(255)
                            ->helperText('Image URL for social media sharing (leave empty to use featured image)'),

                        Forms\Components\FileUpload::make('featured_image')
                            ->label('Featured Image')
                            ->image()
                            ->directory('cms-pages')
                            ->visibility('public')
                            ->imageEditor()
                            ->imageEditorAspectRatios([
                                '16:9',
                                '4:3',
                                '1:1',
                            ])
                            ->helperText('Upload a featured image for this page'),
                    ])->columns(2)->collapsible(),

                Forms\Components\Section::make('Publishing Options')
                    ->description('Control when and how this page is published')
                    ->schema([
                        Forms\Components\Toggle::make('is_published')
                            ->label('Published')
                            ->default(false)
                            ->helperText('Make this page visible to the public')
                            ->live()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                if ($state) {
                                    $set('published_at', now());
                                }
                            }),

                        Forms\Components\Toggle::make('is_featured')
                            ->label('Featured')
                            ->default(false)
                            ->helperText('Mark this page as featured content'),

                        Forms\Components\DateTimePicker::make('published_at')
                            ->label('Publish Date')
                            ->default(now())
                            ->helperText('When this page should be published')
                            ->visible(fn (Forms\Get $get) => $get('is_published')),

                        Forms\Components\DateTimePicker::make('scheduled_publish_at')
                            ->label('Scheduled Publish Date')
                            ->helperText('Schedule this page to be published automatically at a future date'),

                        Forms\Components\DateTimePicker::make('scheduled_unpublish_at')
                            ->label('Scheduled Unpublish Date')
                            ->helperText('Schedule this page to be unpublished automatically at a future date'),

                        Forms\Components\TextInput::make('sort_order')
                            ->label('Sort Order')
                            ->numeric()
                            ->default(0)
                            ->minValue(0)
                            ->maxValue(9999)
                            ->helperText('Order for page listing (0 = first)'),

                        Forms\Components\Select::make('created_by')
                            ->label('Author')
                            ->relationship('creator', 'name')
                            ->default(auth()->id())
                            ->required()
                            ->disabled()
                            ->dehydrated()
                            ->helperText('The user who created this page'),
                    ])->columns(2)->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable()
                    ->limit(40)
                    ->weight(FontWeight::SemiBold)
                    ->description(fn (CmsPage $record): string => $record->slug),

                Tables\Columns\BadgeColumn::make('template')
                    ->label('Template')
                    ->colors([
                        'primary' => 'default',
                        'success' => 'landing',
                        'warning' => 'blog',
                        'info' => 'minimal',
                    ])
                    ->sortable(),

                Tables\Columns\BadgeColumn::make('status')
                    ->label('Status')
                    ->colors([
                        'gray' => 'draft',
                        'success' => 'published',
                        'danger' => 'archived',
                    ])
                    ->icons([
                        'heroicon-o-pencil' => 'draft',
                        'heroicon-o-check-circle' => 'published',
                        'heroicon-o-archive-box' => 'archived',
                    ])
                    ->sortable(),

                Tables\Columns\BadgeColumn::make('editor_type')
                    ->label('Editor')
                    ->colors([
                        'primary' => 'html',
                        'success' => 'puck',
                    ])
                    ->icons([
                        'heroicon-o-code-bracket' => 'html',
                        'heroicon-o-squares-2x2' => 'puck',
                    ])
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_published')
                    ->boolean()
                    ->label('Status')
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger')
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_featured')
                    ->boolean()
                    ->label('Featured')
                    ->trueIcon('heroicon-o-star')
                    ->falseIcon('heroicon-o-star')
                    ->trueColor('warning')
                    ->falseColor('gray')
                    ->sortable(),

                Tables\Columns\ImageColumn::make('featured_image')
                    ->label('Image')
                    ->circular()
                    ->defaultImageUrl(url('/images/placeholder.png'))
                    ->toggleable(),

                Tables\Columns\TextColumn::make('sort_order')
                    ->label('Order')
                    ->sortable()
                    ->alignCenter()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('creator.name')
                    ->label('Author')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('published_at')
                    ->label('Published')
                    ->dateTime('M j, Y')
                    ->sortable()
                    ->placeholder('Not published')
                    ->toggleable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime('M j, Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Updated')
                    ->dateTime('M j, Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_published')
                    ->label('Publication Status')
                    ->placeholder('All pages')
                    ->trueLabel('Published only')
                    ->falseLabel('Drafts only'),

                Tables\Filters\TernaryFilter::make('is_featured')
                    ->label('Featured Status')
                    ->placeholder('All pages')
                    ->trueLabel('Featured only')
                    ->falseLabel('Not featured'),

                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'draft' => 'Draft',
                        'published' => 'Published',
                        'archived' => 'Archived',
                    ])
                    ->placeholder('All statuses'),

                Tables\Filters\SelectFilter::make('template')
                    ->label('Template')
                    ->options([
                        'default' => 'Default',
                        'landing' => 'Landing Page',
                        'blog' => 'Blog Post',
                        'minimal' => 'Minimal',
                    ])
                    ->placeholder('All templates'),

                Tables\Filters\SelectFilter::make('editor_type')
                    ->label('Editor Type')
                    ->options([
                        'html' => 'HTML Editor',
                        'puck' => 'Puck Visual Editor',
                    ])
                    ->placeholder('All editor types'),

                Tables\Filters\SelectFilter::make('created_by')
                    ->relationship('creator', 'name')
                    ->label('Author')
                    ->placeholder('All authors')
                    ->searchable()
                    ->preload(),

                Tables\Filters\Filter::make('published_date_range')
                    ->form([
                        Forms\Components\DatePicker::make('published_from')
                            ->label('Published from'),
                        Forms\Components\DatePicker::make('published_until')
                            ->label('Published until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['published_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('published_at', '>=', $date),
                            )
                            ->when(
                                $data['published_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('published_at', '<=', $date),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['published_from'] ?? null) {
                            $indicators['published_from'] = 'Published from ' . \Carbon\Carbon::parse($data['published_from'])->toFormattedDateString();
                        }
                        if ($data['published_until'] ?? null) {
                            $indicators['published_until'] = 'Published until ' . \Carbon\Carbon::parse($data['published_until'])->toFormattedDateString();
                        }
                        return $indicators;
                    }),
            ], layout: Tables\Enums\FiltersLayout::AboveContent)
            ->filtersFormColumns(2)
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make()
                        ->color('info'),

                    Tables\Actions\Action::make('preview')
                        ->label('Preview')
                        ->icon('heroicon-o-eye')
                        ->color('success')
                        ->url(fn (CmsPage $record): string => url("/api/pages/{$record->slug}"))
                        ->openUrlInNewTab()
                        ->visible(fn (CmsPage $record): bool => $record->is_published),

                    Tables\Actions\EditAction::make()
                        ->color('warning'),

                    Tables\Actions\Action::make('toggle_published')
                        ->label(fn (CmsPage $record): string => $record->is_published ? 'Unpublish' : 'Publish')
                        ->icon(fn (CmsPage $record): string => $record->is_published ? 'heroicon-o-eye-slash' : 'heroicon-o-eye')
                        ->color(fn (CmsPage $record): string => $record->is_published ? 'danger' : 'success')
                        ->requiresConfirmation()
                        ->modalDescription(fn (CmsPage $record): string => $record->is_published
                            ? 'This will make the page unavailable to the public.'
                            : 'This will make the page visible to the public.')
                        ->visible(fn (CmsPage $record): bool => auth()->user()->can('publish', $record))
                        ->action(function (CmsPage $record) {
                            $record->update([
                                'is_published' => !$record->is_published,
                                'published_at' => !$record->is_published ? null : ($record->published_at ?? now()),
                                'updated_by' => auth()->id(),
                            ]);

                            Notification::make()
                                ->title($record->is_published ? 'Page published successfully' : 'Page unpublished successfully')
                                ->success()
                                ->send();
                        }),

                    Tables\Actions\Action::make('toggle_featured')
                        ->label(fn (CmsPage $record): string => $record->is_featured ? 'Remove from Featured' : 'Mark as Featured')
                        ->icon(fn (CmsPage $record): string => $record->is_featured ? 'heroicon-o-star' : 'heroicon-s-star')
                        ->color('warning')
                        ->visible(fn (CmsPage $record): bool => auth()->user()->can('feature', $record))
                        ->action(function (CmsPage $record) {
                            $record->update([
                                'is_featured' => !$record->is_featured,
                                'updated_by' => auth()->id(),
                            ]);

                            Notification::make()
                                ->title($record->is_featured ? 'Page marked as featured' : 'Page removed from featured')
                                ->success()
                                ->send();
                        }),

                    Tables\Actions\Action::make('duplicate')
                        ->label('Duplicate')
                        ->icon('heroicon-o-document-duplicate')
                        ->color('gray')
                        ->requiresConfirmation()
                        ->modalDescription('This will create a copy of this page.')
                        ->visible(fn (CmsPage $record): bool => auth()->user()->can('duplicate', $record))
                        ->action(function (CmsPage $record) {
                            $newPage = $record->replicate();
                            $newPage->title = $record->title . ' (Copy)';
                            $newPage->slug = $record->slug . '-copy-' . time();
                            $newPage->is_published = false;
                            $newPage->is_featured = false;
                            $newPage->published_at = null;
                            $newPage->created_by = auth()->id();
                            $newPage->updated_by = auth()->id();
                            $newPage->save();

                            Notification::make()
                                ->title('Page duplicated successfully')
                                ->success()
                                ->send();
                        }),

                    Tables\Actions\DeleteAction::make()
                        ->requiresConfirmation()
                        ->modalDescription('This action cannot be undone.'),
                ])
                ->label('Actions')
                ->icon('heroicon-m-ellipsis-vertical')
                ->size('sm')
                ->color('gray')
                ->button(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('publish')
                        ->label('Publish Selected')
                        ->icon('heroicon-o-eye')
                        ->color('success')
                        ->requiresConfirmation()
                        ->modalDescription('This will publish all selected pages.')
                        ->visible(fn () => auth()->user()->can('publishAny', CmsPage::class))
                        ->action(function (Collection $records) {
                            $records->each(function (CmsPage $record) {
                                if (auth()->user()->can('publish', $record)) {
                                    $record->update([
                                        'is_published' => true,
                                        'published_at' => $record->published_at ?? now(),
                                        'updated_by' => auth()->id(),
                                    ]);
                                }
                            });

                            Notification::make()
                                ->title('Pages published successfully')
                                ->success()
                                ->send();
                        }),

                    Tables\Actions\BulkAction::make('unpublish')
                        ->label('Unpublish Selected')
                        ->icon('heroicon-o-eye-slash')
                        ->color('danger')
                        ->requiresConfirmation()
                        ->modalDescription('This will unpublish all selected pages.')
                        ->visible(fn () => auth()->user()->can('unpublishAny', CmsPage::class))
                        ->action(function (Collection $records) {
                            $records->each(function (CmsPage $record) {
                                if (auth()->user()->can('publish', $record)) {
                                    $record->update([
                                        'is_published' => false,
                                        'updated_by' => auth()->id(),
                                    ]);
                                }
                            });

                            Notification::make()
                                ->title('Pages unpublished successfully')
                                ->success()
                                ->send();
                        }),

                    Tables\Actions\BulkAction::make('feature')
                        ->label('Mark as Featured')
                        ->icon('heroicon-o-star')
                        ->color('warning')
                        ->requiresConfirmation()
                        ->modalDescription('This will mark all selected pages as featured.')
                        ->visible(fn () => auth()->user()->can('featureAny', CmsPage::class))
                        ->action(function (Collection $records) {
                            $records->each(function (CmsPage $record) {
                                if (auth()->user()->can('feature', $record)) {
                                    $record->update([
                                        'is_featured' => true,
                                        'updated_by' => auth()->id(),
                                    ]);
                                }
                            });

                            Notification::make()
                                ->title('Pages marked as featured')
                                ->success()
                                ->send();
                        }),

                    Tables\Actions\BulkAction::make('unfeature')
                        ->label('Remove from Featured')
                        ->icon('heroicon-s-star')
                        ->color('gray')
                        ->requiresConfirmation()
                        ->modalDescription('This will remove all selected pages from featured.')
                        ->visible(fn () => auth()->user()->can('unfeatureAny', CmsPage::class))
                        ->action(function (Collection $records) {
                            $records->each(function (CmsPage $record) {
                                if (auth()->user()->can('feature', $record)) {
                                    $record->update([
                                        'is_featured' => false,
                                        'updated_by' => auth()->id(),
                                    ]);
                                }
                            });

                            Notification::make()
                                ->title('Pages removed from featured')
                                ->success()
                                ->send();
                        }),

                    Tables\Actions\DeleteBulkAction::make()
                        ->requiresConfirmation()
                        ->modalDescription('This action cannot be undone.'),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->poll('30s')
            ->deferLoading()
            ->striped();
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCmsPages::route('/'),
            'create' => Pages\CreateCmsPage::route('/create'),
            'edit' => Pages\EditCmsPage::route('/{record}/edit'),
        ];
    }
}
