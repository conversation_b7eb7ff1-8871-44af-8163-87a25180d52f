<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\CmsPage;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Traits\ApiResponseTrait;

class CmsPageController extends Controller
{
    use ApiResponseTrait;
    /**
     * Get published CMS pages
     */
    public function index(Request $request): JsonResponse
    {
        $query = CmsPage::published()
            ->with(['creator:id,name', 'updater:id,name']);

        // Filter by featured if requested
        if ($request->boolean('featured')) {
            $query->featured();
        }

        // Filter by template if requested
        if ($request->filled('template')) {
            $query->byTemplate($request->get('template'));
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%")
                  ->orWhere('rendered_content', 'like', "%{$search}%")
                  ->orWhere('meta_description', 'like', "%{$search}%")
                  ->orWhere('meta_title', 'like', "%{$search}%");
            });
        }

        // Order by sort_order first, then by published date
        $query->ordered();

        $perPage = $request->get('per_page', 15);
        $pages = $query->paginate($perPage);

        // Transform the data to include SEO fields
        $transformedPages = $pages->getCollection()->map(function ($page) {
            return [
                'id' => $page->id,
                'title' => $page->title,
                'slug' => $page->slug,
                'meta_title' => $page->getEffectiveMetaTitle(),
                'meta_description' => $page->getEffectiveMetaDescription(),
                'meta_keywords' => $page->meta_keywords,
                'og_title' => $page->getOgTitle(),
                'og_description' => $page->getOgDescription(),
                'og_image' => $page->getOgImage(),
                'canonical_url' => $page->canonical_url,
                'status' => $page->status,
                'template' => $page->template,
                'is_published' => $page->isPublished(),
                'is_featured' => $page->is_featured,
                'featured_image' => $page->featured_image,
                'sort_order' => $page->sort_order,
                'editor_type' => $page->editor_type,
                'published_at' => $page->published_at,
                'created_at' => $page->created_at,
                'updated_at' => $page->updated_at,
                'creator' => $page->creator,
                'updater' => $page->updater,
            ];
        });

        return $this->paginatedResponse(
            $pages->setCollection($transformedPages),
            'Pages retrieved successfully'
        );
    }

    /**
     * Get a specific CMS page by slug
     */
    public function show(string $slug): JsonResponse
    {
        $page = CmsPage::published()
            ->with(['creator:id,name', 'updater:id,name'])
            ->where('slug', $slug)
            ->first();

        if (!$page) {
            return $this->notFoundResponse('Page not found');
        }

        // Transform the page data to include all SEO and content fields
        $pageData = [
            'id' => $page->id,
            'title' => $page->title,
            'slug' => $page->slug,
            'content' => $page->getDisplayContent(),
            'editor_type' => $page->editor_type,
            'puck_data' => $page->isPuckPage() ? $page->puck_data : null,
            'rendered_content' => $page->rendered_content,
            'template' => $page->template,
            'status' => $page->status,
            'is_published' => $page->isPublished(),
            'is_featured' => $page->is_featured,
            'featured_image' => $page->featured_image,
            'sort_order' => $page->sort_order,
            'seo' => [
                'meta_title' => $page->getEffectiveMetaTitle(),
                'meta_description' => $page->getEffectiveMetaDescription(),
                'meta_keywords' => $page->meta_keywords,
                'meta_robots' => $page->meta_robots,
                'canonical_url' => $page->canonical_url,
                'og_title' => $page->getOgTitle(),
                'og_description' => $page->getOgDescription(),
                'og_image' => $page->getOgImage(),
            ],
            'published_at' => $page->published_at,
            'created_at' => $page->created_at,
            'updated_at' => $page->updated_at,
            'creator' => $page->creator,
            'updater' => $page->updater,
        ];

        return $this->successResponse($pageData, 'Page retrieved successfully');
    }

    /**
     * Get all pages (including drafts) - for admin use
     */
    public function all(Request $request): JsonResponse
    {
        // This endpoint should be protected for admin users only
        // Add middleware or authorization check here if needed

        $query = CmsPage::with(['creator:id,name', 'updater:id,name']);

        // Filter by status if requested
        if ($request->filled('status')) {
            $query->byStatus($request->get('status'));
        }

        // Filter by template if requested
        if ($request->filled('template')) {
            $query->byTemplate($request->get('template'));
        }

        // Filter by featured if requested
        if ($request->boolean('featured')) {
            $query->featured();
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%")
                  ->orWhere('rendered_content', 'like', "%{$search}%")
                  ->orWhere('meta_description', 'like', "%{$search}%")
                  ->orWhere('meta_title', 'like', "%{$search}%");
            });
        }

        // Order by sort_order first, then by created date
        $query->ordered();

        $perPage = $request->get('per_page', 15);
        $pages = $query->paginate($perPage);

        return $this->paginatedResponse($pages, 'All pages retrieved successfully');
    }

    /**
     * Get available templates
     */
    public function templates(): JsonResponse
    {
        $templates = [
            'default' => [
                'name' => 'Default',
                'description' => 'Standard page layout with header and content area',
                'preview' => '/images/templates/default.png'
            ],
            'landing' => [
                'name' => 'Landing Page',
                'description' => 'Full-width landing page with hero section',
                'preview' => '/images/templates/landing.png'
            ],
            'blog' => [
                'name' => 'Blog Post',
                'description' => 'Blog post layout with sidebar and metadata',
                'preview' => '/images/templates/blog.png'
            ],
            'minimal' => [
                'name' => 'Minimal',
                'description' => 'Clean, minimal layout with focus on content',
                'preview' => '/images/templates/minimal.png'
            ]
        ];

        return $this->successResponse($templates, 'Templates retrieved successfully');
    }

    /**
     * Get pages by template
     */
    public function byTemplate(string $template, Request $request): JsonResponse
    {
        $query = CmsPage::published()
            ->byTemplate($template)
            ->with(['creator:id,name', 'updater:id,name'])
            ->ordered();

        $perPage = $request->get('per_page', 15);
        $pages = $query->paginate($perPage);

        return $this->paginatedResponse($pages, "Pages with template '{$template}' retrieved successfully");
    }
}
