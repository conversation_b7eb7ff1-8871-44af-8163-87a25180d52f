<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\CmsPageController;
use App\Http\Controllers\Api\CreditController;
use App\Http\Controllers\Api\PaymentController;
use App\Http\Controllers\Api\PrintingController;
use App\Http\Controllers\Api\OrderController;
use App\Http\Controllers\Api\FileUploadController;
use App\Http\Controllers\Api\GeneralSettingsController;
use App\Http\Controllers\Api\Admin\CmsPageController as AdminCmsPageController;

// Public routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);
Route::post('/forgot-password', [AuthController::class, 'forgotPassword']);
Route::post('/reset-password', [AuthController::class, 'resetPassword']);

// Test route for CORS
Route::get('/test-cors', function (Request $request) {
    return response()->json([
        'success' => true,
        'message' => 'CORS is working correctly',
        'origin' => $request->headers->get('Origin'),
        'host' => $request->getHost(),
        'timestamp' => now()->toISOString(),
    ]);
});

// Email verification routes
Route::get('/email/verify/{id}/{hash}', [AuthController::class, 'verifyEmail'])
    ->middleware(['signed'])
    ->name('verification.verify');
Route::post('/email/verification-notification', [AuthController::class, 'resendVerification'])
    ->middleware(['auth:sanctum', 'throttle:6,1']);

// Public CMS routes
Route::get('/pages', [CmsPageController::class, 'index']);
Route::get('/pages/{slug}', [CmsPageController::class, 'show']);
Route::get('/pages/template/{template}', [CmsPageController::class, 'byTemplate']);
Route::get('/cms/templates', [CmsPageController::class, 'templates']);

// Public payment routes (for callbacks)
Route::post('/billplz/callback', [PaymentController::class, 'billplzCallback'])->name('api.billplz.callback');
Route::get('/payment/config', [PaymentController::class, 'getPaymentConfig']);

// Public printing routes
Route::prefix('printing')->group(function () {
    Route::get('/categories', [PrintingController::class, 'categories']);
    Route::get('/products', [PrintingController::class, 'products']);
    Route::get('/products/category/{categorySlug}', [PrintingController::class, 'products']);
    Route::get('/product/{slug}', [PrintingController::class, 'product']);
    Route::post('/calculate-price', [PrintingController::class, 'calculatePrice']);
});

// Public file upload settings route
Route::get('/file-upload/settings', [FileUploadController::class, 'getUploadSettings']);

// Public general settings routes
Route::prefix('settings')->group(function () {
    Route::get('/', [GeneralSettingsController::class, 'index']);
    Route::get('/seo-meta', [GeneralSettingsController::class, 'seoMeta']);
    Route::get('/branding', [GeneralSettingsController::class, 'branding']);
    Route::get('/{key}', [GeneralSettingsController::class, 'show']);
});

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // User routes
    Route::get('/user', [UserController::class, 'profile']);
    Route::put('/user', [UserController::class, 'updateProfile']);
    Route::post('/user/avatar', [UserController::class, 'uploadAvatar']);
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/admin-status', [AuthController::class, 'checkAdminStatus']);
    Route::post('/admin-session', [AuthController::class, 'getAdminSession']);

    // Email verification status
    Route::get('/email/verify', function (Request $request) {
        return $request->user()->hasVerifiedEmail()
            ? response()->json(['message' => 'Email already verified'])
            : response()->json(['message' => 'Email not verified'], 403);
    });

    // Credit routes
    Route::prefix('credit')->group(function () {
        Route::get('/balance', [CreditController::class, 'balance']);
        Route::get('/packages', [CreditController::class, 'packages']);
        Route::get('/transactions', [CreditController::class, 'transactions']);
        Route::get('/statistics', [CreditController::class, 'statistics']);
    });

    // Payment routes
    Route::prefix('payment')->group(function () {
        Route::post('/create', [PaymentController::class, 'createPayment']);
        Route::get('/status', [PaymentController::class, 'checkPaymentStatus']);
    });

    // Admin routes (require admin role)
    Route::prefix('admin')->middleware('auth:sanctum')->group(function () {
        // General settings cache management
        Route::post('/settings/clear-cache', [GeneralSettingsController::class, 'clearCache']);
        // User management routes
        Route::prefix('users')->group(function () {
            Route::get('/{id}/balance', [App\Http\Controllers\Api\Admin\UserController::class, 'getBalance']);
        });

        // Wallet transaction routes (new)
        Route::prefix('wallet-transactions')->group(function () {
            Route::post('/', [App\Http\Controllers\Api\Admin\WalletTransactionController::class, 'store']);
            Route::get('/types', [App\Http\Controllers\Api\Admin\WalletTransactionController::class, 'getTransactionTypes']);
            Route::post('/verify-balance', [App\Http\Controllers\Api\Admin\WalletTransactionController::class, 'verifyBalance']);
            Route::get('/balance-breakdown', [App\Http\Controllers\Api\Admin\WalletTransactionController::class, 'getBalanceBreakdown']);
            Route::post('/bulk-verify-balances', [App\Http\Controllers\Api\Admin\WalletTransactionController::class, 'bulkVerifyBalances']);
            Route::get('/statistics', [App\Http\Controllers\Api\Admin\WalletTransactionController::class, 'getStatistics']);

            // Specific transaction type endpoints
            Route::post('/top-up', [App\Http\Controllers\Api\Admin\WalletTransactionController::class, 'createTopUp']);
            Route::post('/payment', [App\Http\Controllers\Api\Admin\WalletTransactionController::class, 'createPayment']);
            Route::post('/withdrawal', [App\Http\Controllers\Api\Admin\WalletTransactionController::class, 'createWithdrawal']);
            Route::post('/refund', [App\Http\Controllers\Api\Admin\WalletTransactionController::class, 'createRefund']);
            Route::post('/bonus', [App\Http\Controllers\Api\Admin\WalletTransactionController::class, 'createBonus']);
            Route::post('/adjustment', [App\Http\Controllers\Api\Admin\WalletTransactionController::class, 'createAdjustment']);
        });

        // Legacy credit transaction routes (for backward compatibility)
        Route::prefix('credit-transactions')->group(function () {
            Route::post('/', [App\Http\Controllers\Api\Admin\WalletTransactionController::class, 'store']);
            Route::get('/types', [App\Http\Controllers\Api\Admin\WalletTransactionController::class, 'getTransactionTypes']);
            Route::post('/verify-balance', [App\Http\Controllers\Api\Admin\WalletTransactionController::class, 'verifyBalance']);
            Route::get('/balance-breakdown', [App\Http\Controllers\Api\Admin\WalletTransactionController::class, 'getBalanceBreakdown']);
            Route::post('/bulk-verify-balances', [App\Http\Controllers\Api\Admin\WalletTransactionController::class, 'bulkVerifyBalances']);
            Route::get('/statistics', [App\Http\Controllers\Api\Admin\WalletTransactionController::class, 'getStatistics']);

            // Legacy specific transaction type endpoints
            Route::post('/purchase', [App\Http\Controllers\Api\Admin\WalletTransactionController::class, 'createTopUp']);
            Route::post('/usage', [App\Http\Controllers\Api\Admin\WalletTransactionController::class, 'createPayment']);
            Route::post('/refund', [App\Http\Controllers\Api\Admin\WalletTransactionController::class, 'createRefund']);
            Route::post('/bonus', [App\Http\Controllers\Api\Admin\WalletTransactionController::class, 'createBonus']);
            Route::post('/adjustment', [App\Http\Controllers\Api\Admin\WalletTransactionController::class, 'createAdjustment']);
        });

        // CMS page management routes
        Route::prefix('cms-pages')->group(function () {
            Route::get('/', [AdminCmsPageController::class, 'index']);
            Route::post('/', [AdminCmsPageController::class, 'store']);
            Route::get('/statistics', [AdminCmsPageController::class, 'statistics']);
            Route::get('/{id}', [AdminCmsPageController::class, 'show']);
            Route::put('/{id}', [AdminCmsPageController::class, 'update']);
            Route::delete('/{id}', [AdminCmsPageController::class, 'destroy']);
            Route::post('/{id}/toggle-published', [AdminCmsPageController::class, 'togglePublished']);
            Route::post('/{id}/toggle-featured', [AdminCmsPageController::class, 'toggleFeatured']);

            // Puck-specific routes
            Route::get('/{id}/puck-data', [AdminCmsPageController::class, 'getPuckData']);
            Route::post('/{id}/puck-data', [AdminCmsPageController::class, 'savePuckData']);
            Route::post('/{id}/switch-editor', [AdminCmsPageController::class, 'switchEditorType']);
            Route::post('/convert/html-to-puck', [AdminCmsPageController::class, 'convertHtmlToPuck']);
            Route::post('/convert/puck-to-html', [AdminCmsPageController::class, 'convertPuckToHtml']);
            Route::post('/validate-puck-data', [AdminCmsPageController::class, 'validatePuckData']);
        });
    });

    // Order routes
    Route::prefix('orders')->group(function () {
        Route::get('/', [OrderController::class, 'index']);
        Route::post('/', [OrderController::class, 'store']);
        Route::get('/{id}', [OrderController::class, 'show']);
        Route::get('/{id}/payment-info', [OrderController::class, 'getPaymentInfo']);
        Route::post('/{id}/pay', [OrderController::class, 'processPayment']);
        Route::post('/{id}/cancel', [OrderController::class, 'cancel']);
        Route::post('/{id}/reorder', [OrderController::class, 'reorder']);
        Route::post('/{id}/approve', [OrderController::class, 'approveOrder']); // Admin only
        Route::put('/{id}/status', [OrderController::class, 'updateStatus']); // Admin only

        // File upload routes
        Route::post('/{orderId}/files', [FileUploadController::class, 'upload']);
        Route::get('/{orderId}/files', [FileUploadController::class, 'getFiles']);
        Route::delete('/{orderId}/files/{fileId}', [FileUploadController::class, 'deleteFile']);
        Route::post('/{orderId}/files/{fileId}/reupload', [OrderController::class, 'reUploadFile']);
        Route::get('/{orderId}/files/{fileId}/history', [FileUploadController::class, 'getFileHistory']);
        Route::get('/{orderId}/files/{fileId}/reupload-permissions', [FileUploadController::class, 'getReUploadPermissions']);
        Route::get('/{orderId}/files/{fileId}/download', [FileUploadController::class, 'downloadFile'])
            ->name('api.orders.files.download')
            ->middleware('admin');
        Route::get('/{orderId}/files/download-all', [FileUploadController::class, 'downloadAllFiles'])
            ->name('api.orders.files.download-all')
            ->middleware('admin');
    });
});
