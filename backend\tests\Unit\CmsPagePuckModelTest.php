<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\CmsPage;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

class CmsPagePuckModelTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create([
            'role' => 'admin',
            'is_active' => true,
        ]);
    }

    /** @test */
    public function it_can_create_puck_page()
    {
        $puckData = [
            'content' => [
                [
                    'type' => 'Hero',
                    'props' => [
                        'id' => 'hero-123',
                        'title' => 'Test Hero',
                    ]
                ]
            ]
        ];

        $page = CmsPage::create([
            'title' => 'Test Puck Page',
            'editor_type' => 'puck',
            'puck_data' => $puckData,
            'rendered_content' => '<section>Test Hero</section>',
            'content' => '<section>Test Hero</section>',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $this->assertTrue($page->isPuckPage());
        $this->assertFalse($page->isHtmlPage());
        $this->assertEquals($puckData, $page->puck_data);
    }

    /** @test */
    public function it_can_create_html_page()
    {
        $page = CmsPage::create([
            'title' => 'Test HTML Page',
            'editor_type' => 'html',
            'content' => '<h1>HTML Content</h1>',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $this->assertTrue($page->isHtmlPage());
        $this->assertFalse($page->isPuckPage());
        $this->assertNull($page->puck_data);
    }

    /** @test */
    public function it_defaults_to_html_editor_type()
    {
        $page = CmsPage::create([
            'title' => 'Default Page',
            'content' => '<p>Content</p>',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $this->assertEquals('html', $page->editor_type);
        $this->assertTrue($page->isHtmlPage());
    }

    /** @test */
    public function puck_pages_scope_returns_only_puck_pages()
    {
        $puckPage = CmsPage::create([
            'title' => 'Puck Page',
            'editor_type' => 'puck',
            'content' => '<p>Content</p>',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $htmlPage = CmsPage::create([
            'title' => 'HTML Page',
            'editor_type' => 'html',
            'content' => '<p>Content</p>',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $puckPages = CmsPage::puckPages()->get();

        $this->assertCount(1, $puckPages);
        $this->assertTrue($puckPages->contains($puckPage));
        $this->assertFalse($puckPages->contains($htmlPage));
    }

    /** @test */
    public function html_pages_scope_returns_only_html_pages()
    {
        $puckPage = CmsPage::create([
            'title' => 'Puck Page',
            'editor_type' => 'puck',
            'content' => '<p>Content</p>',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $htmlPage = CmsPage::create([
            'title' => 'HTML Page',
            'editor_type' => 'html',
            'content' => '<p>Content</p>',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $htmlPages = CmsPage::htmlPages()->get();

        $this->assertCount(1, $htmlPages);
        $this->assertTrue($htmlPages->contains($htmlPage));
        $this->assertFalse($htmlPages->contains($puckPage));
    }

    /** @test */
    public function get_display_content_returns_rendered_content_for_puck_pages()
    {
        $page = CmsPage::create([
            'title' => 'Puck Page',
            'editor_type' => 'puck',
            'content' => '<p>Original content</p>',
            'rendered_content' => '<section>Rendered Puck content</section>',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $this->assertEquals('<section>Rendered Puck content</section>', $page->getDisplayContent());
    }

    /** @test */
    public function get_display_content_returns_content_for_html_pages()
    {
        $page = CmsPage::create([
            'title' => 'HTML Page',
            'editor_type' => 'html',
            'content' => '<h1>HTML content</h1>',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $this->assertEquals('<h1>HTML content</h1>', $page->getDisplayContent());
    }

    /** @test */
    public function it_can_render_puck_content_to_html()
    {
        $puckData = [
            'content' => [
                [
                    'type' => 'Hero',
                    'props' => [
                        'id' => 'hero-123',
                        'title' => 'Test Hero',
                        'subtitle' => 'Test subtitle',
                        'textAlign' => 'center',
                    ]
                ]
            ]
        ];

        $page = CmsPage::create([
            'title' => 'Puck Page',
            'editor_type' => 'puck',
            'puck_data' => $puckData,
            'content' => '<p>Content</p>',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $renderedHtml = $page->renderPuckContent();

        $this->assertIsString($renderedHtml);
        $this->assertStringContainsString('Test Hero', $renderedHtml);
        $this->assertStringContainsString('Test subtitle', $renderedHtml);
    }

    /** @test */
    public function it_can_convert_html_to_puck_format()
    {
        $page = CmsPage::create([
            'title' => 'HTML Page',
            'editor_type' => 'html',
            'content' => '<h1>HTML Title</h1><p>HTML content</p>',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $puckData = $page->convertHtmlToPuck();

        $this->assertIsArray($puckData);
        $this->assertArrayHasKey('content', $puckData);
        $this->assertArrayHasKey('root', $puckData);
        $this->assertNotEmpty($puckData['content']);
        
        // Should have at least one component
        $this->assertGreaterThan(0, count($puckData['content']));
        
        // First component should have required structure
        $firstComponent = $puckData['content'][0];
        $this->assertArrayHasKey('type', $firstComponent);
        $this->assertArrayHasKey('props', $firstComponent);
        $this->assertArrayHasKey('id', $firstComponent['props']);
    }

    /** @test */
    public function it_can_switch_from_html_to_puck_editor()
    {
        $page = CmsPage::create([
            'title' => 'HTML Page',
            'editor_type' => 'html',
            'content' => '<h1>HTML Content</h1>',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $result = $page->switchEditorType('puck');

        $this->assertTrue($result);
        $this->assertEquals('puck', $page->editor_type);
        $this->assertNotNull($page->puck_data);
        $this->assertNotNull($page->rendered_content);
        $this->assertEquals('<h1>HTML Content</h1>', $page->rendered_content);
    }

    /** @test */
    public function it_can_switch_from_puck_to_html_editor()
    {
        $puckData = [
            'content' => [
                [
                    'type' => 'Text',
                    'props' => [
                        'id' => 'text-123',
                        'content' => 'Puck content',
                    ]
                ]
            ]
        ];

        $page = CmsPage::create([
            'title' => 'Puck Page',
            'editor_type' => 'puck',
            'puck_data' => $puckData,
            'rendered_content' => '<div>Puck content</div>',
            'content' => '<div>Original</div>',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $result = $page->switchEditorType('html');

        $this->assertTrue($result);
        $this->assertEquals('html', $page->editor_type);
        $this->assertNull($page->puck_data);
        $this->assertNull($page->rendered_content);
        $this->assertEquals('<div>Puck content</div>', $page->content);
    }

    /** @test */
    public function it_returns_true_when_switching_to_same_editor_type()
    {
        $page = CmsPage::create([
            'title' => 'HTML Page',
            'editor_type' => 'html',
            'content' => '<h1>HTML Content</h1>',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $result = $page->switchEditorType('html');

        $this->assertTrue($result);
        $this->assertEquals('html', $page->editor_type);
    }

    /** @test */
    public function it_returns_false_for_invalid_editor_type()
    {
        $page = CmsPage::create([
            'title' => 'Test Page',
            'editor_type' => 'html',
            'content' => '<p>Content</p>',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $result = $page->switchEditorType('invalid');

        $this->assertFalse($result);
        $this->assertEquals('html', $page->editor_type); // Should remain unchanged
    }

    /** @test */
    public function it_can_update_rendered_content_from_puck_data()
    {
        $puckData = [
            'content' => [
                [
                    'type' => 'Text',
                    'props' => [
                        'id' => 'text-123',
                        'content' => 'Updated content',
                    ]
                ]
            ]
        ];

        $page = CmsPage::create([
            'title' => 'Puck Page',
            'editor_type' => 'puck',
            'puck_data' => $puckData,
            'rendered_content' => '<div>Old content</div>',
            'content' => '<div>Old content</div>',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $page->updateRenderedContent();
        $page->refresh();

        $this->assertStringContainsString('Updated content', $page->rendered_content);
    }

    /** @test */
    public function it_casts_puck_data_and_meta_to_arrays()
    {
        $puckData = [
            'content' => [
                [
                    'type' => 'Text',
                    'props' => [
                        'id' => 'text-123',
                        'content' => 'Test',
                    ]
                ]
            ]
        ];

        $puckMeta = [
            'version' => '1.0',
            'theme' => 'default',
        ];

        $page = CmsPage::create([
            'title' => 'Test Page',
            'editor_type' => 'puck',
            'puck_data' => $puckData,
            'puck_meta' => $puckMeta,
            'content' => '<p>Content</p>',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $this->assertIsArray($page->puck_data);
        $this->assertIsArray($page->puck_meta);
        $this->assertEquals($puckData, $page->puck_data);
        $this->assertEquals($puckMeta, $page->puck_meta);
    }
}
