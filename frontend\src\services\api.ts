import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default api;

// API endpoints
export const endpoints = {
  // Auth endpoints
  register: '/register',
  login: '/login',
  logout: '/logout',
  forgotPassword: '/forgot-password',
  resetPassword: '/reset-password',
  verifyEmail: '/email/verify',
  resendVerification: '/email/verification-notification',

  // User endpoints
  profile: '/user',
  updateProfile: '/user',
  uploadAvatar: '/user/avatar',
  adminStatus: '/admin-status',
  adminSession: '/admin-session',



  // Credit endpoints
  creditBalance: '/credit/balance',
  creditPackages: '/credit/packages',
  creditTransactions: '/credit/transactions',
  creditStatistics: '/credit/statistics',

  // Payment endpoints
  createPayment: '/payment/create',
  checkPaymentStatus: '/payment/status',
  paymentConfig: '/payment/config',
  billplzCallback: '/billplz/callback',

  // Printing endpoints
  printing: '/printing',
  orders: '/orders',

  // Settings endpoints
  settings: '/settings',
  settingsSeoMeta: '/settings/seo-meta',
  settingsBranding: '/settings/branding',
  settingByKey: (key: string) => `/settings/${key}`,
};
