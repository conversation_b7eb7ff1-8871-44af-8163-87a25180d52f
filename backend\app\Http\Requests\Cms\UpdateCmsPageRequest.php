<?php

namespace App\Http\Requests\Cms;

use App\Http\Requests\BaseRequest;
use App\Models\CmsPage;

class UpdateCmsPageRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only admin users can update CMS pages
        return auth()->check() && auth()->user()->isAdmin();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $commonRules = $this->getCommonRules();
        $pageId = $this->route('id') ?? $this->route('page');
        
        return [
            'title' => 'sometimes|' . str_replace('required|', '', $commonRules['name_rule']),
            'slug' => "nullable|string|max:255|unique:cms_pages,slug,{$pageId}|regex:/^[a-z0-9\-]+$/",
            'content' => 'nullable|string|max:100000',
            'editor_type' => 'sometimes|in:html,puck',
            'puck_data' => 'nullable|array',
            'template' => 'sometimes|string|max:50|in:default,landing,blog,minimal',
            'status' => 'sometimes|in:draft,published,archived',
            
            // SEO fields
            'meta_title' => 'nullable|string|max:60',
            'meta_description' => 'nullable|string|max:160',
            'meta_keywords' => 'nullable|string|max:255',
            'meta_robots' => 'nullable|string|max:100',
            'canonical_url' => 'nullable|url|max:255',
            
            // Open Graph fields
            'og_title' => 'nullable|string|max:60',
            'og_description' => 'nullable|string|max:160',
            'og_image' => 'nullable|url|max:255',
            
            // Publishing fields
            'is_published' => 'sometimes|boolean',
            'is_featured' => 'sometimes|boolean',
            'featured_image' => 'nullable|url|max:255',
            'published_at' => 'nullable|date',
            'scheduled_publish_at' => 'nullable|date|after:now',
            'scheduled_unpublish_at' => 'nullable|date|after:scheduled_publish_at',
            
            // Content management
            'sort_order' => 'sometimes|integer|min:0|max:9999',
            'page_settings' => 'nullable|array',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return array_merge(parent::messages(), [
            'slug.unique' => 'This URL slug is already in use by another page.',
            'slug.regex' => 'URL slug can only contain lowercase letters, numbers, and hyphens.',
            'editor_type.in' => 'Editor type must be either HTML or Puck.',
            'template.in' => 'Invalid template selected.',
            'status.in' => 'Page status must be draft, published, or archived.',
            'meta_title.max' => 'Meta title should not exceed 60 characters for optimal SEO.',
            'meta_description.max' => 'Meta description should not exceed 160 characters for optimal SEO.',
            'canonical_url.url' => 'Canonical URL must be a valid URL.',
            'og_title.max' => 'Open Graph title should not exceed 60 characters.',
            'og_description.max' => 'Open Graph description should not exceed 160 characters.',
            'og_image.url' => 'Open Graph image must be a valid URL.',
            'featured_image.url' => 'Featured image must be a valid URL.',
            'scheduled_publish_at.after' => 'Scheduled publish date must be in the future.',
            'scheduled_unpublish_at.after' => 'Scheduled unpublish date must be after the publish date.',
            'sort_order.min' => 'Sort order must be a positive number.',
            'sort_order.max' => 'Sort order cannot exceed 9999.',
            'content.max' => 'Content is too long. Please reduce the content size.',
        ]);
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return array_merge(parent::attributes(), [
            'meta_title' => 'SEO title',
            'meta_description' => 'SEO description',
            'meta_keywords' => 'SEO keywords',
            'meta_robots' => 'robots directive',
            'canonical_url' => 'canonical URL',
            'og_title' => 'Open Graph title',
            'og_description' => 'Open Graph description',
            'og_image' => 'Open Graph image',
            'is_published' => 'published status',
            'is_featured' => 'featured status',
            'featured_image' => 'featured image',
            'published_at' => 'publish date',
            'scheduled_publish_at' => 'scheduled publish date',
            'scheduled_unpublish_at' => 'scheduled unpublish date',
            'sort_order' => 'sort order',
            'page_settings' => 'page settings',
        ]);
    }

    /**
     * Fields that are allowed to contain HTML
     */
    protected function getAllowedHtmlFields(): array
    {
        return [
            'content',
            'meta_description',
            'og_description',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        parent::prepareForValidation();

        // Auto-generate slug if title is updated but slug is empty
        if ($this->has('title') && empty($this->slug)) {
            $this->merge([
                'slug' => \Illuminate\Support\Str::slug($this->title)
            ]);
        }

        // Handle boolean fields
        if ($this->has('is_published')) {
            $this->merge(['is_published' => $this->boolean('is_published')]);
        }
        if ($this->has('is_featured')) {
            $this->merge(['is_featured' => $this->boolean('is_featured')]);
        }

        // Handle editor type specific validation
        if ($this->has('editor_type')) {
            if ($this->editor_type === 'puck') {
                // For Puck editor, content should be null
                $this->merge(['content' => null]);
            } else {
                // For HTML editor, puck_data should be null
                $this->merge(['puck_data' => null]);
            }
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Additional validation for Puck data
            if ($this->has('editor_type') && $this->editor_type === 'puck' && $this->puck_data) {
                if (!is_array($this->puck_data)) {
                    $validator->errors()->add('puck_data', 'Puck data must be a valid JSON object.');
                }
            }

            // Validate scheduling logic
            if ($this->scheduled_publish_at && $this->scheduled_unpublish_at) {
                if (strtotime($this->scheduled_unpublish_at) <= strtotime($this->scheduled_publish_at)) {
                    $validator->errors()->add('scheduled_unpublish_at', 'Unpublish date must be after publish date.');
                }
            }

            // Auto-set published_at when status changes to published
            if ($this->has('status') && $this->status === 'published' && !$this->published_at) {
                $this->merge(['published_at' => now()]);
            }

            // Validate that published pages have required content
            if (($this->has('status') && $this->status === 'published') || 
                ($this->has('is_published') && $this->is_published)) {
                
                $pageId = $this->route('id') ?? $this->route('page');
                $page = CmsPage::find($pageId);
                
                if ($page) {
                    $hasContent = false;
                    
                    if ($this->has('editor_type')) {
                        $editorType = $this->editor_type;
                    } else {
                        $editorType = $page->editor_type;
                    }
                    
                    if ($editorType === 'puck') {
                        $puckData = $this->has('puck_data') ? $this->puck_data : $page->puck_data;
                        $hasContent = !empty($puckData);
                    } else {
                        $content = $this->has('content') ? $this->content : $page->content;
                        $hasContent = !empty(trim($content));
                    }
                    
                    if (!$hasContent) {
                        $validator->errors()->add('content', 'Published pages must have content.');
                    }
                }
            }
        });
    }
}
