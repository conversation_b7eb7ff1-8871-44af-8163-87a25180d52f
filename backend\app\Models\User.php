<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;

class User extends Authenticatable implements MustVerifyEmail, FilamentUser
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasApiTokens;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'bio',
        'avatar',
        'date_of_birth',
    ];

    /**
     * The attributes that should be guarded from mass assignment.
     *
     * @var list<string>
     */
    protected $guarded = [
        'role',
        'is_active',
        'wallet_balance',
        'email_verified_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'date_of_birth' => 'date',
            'is_active' => 'boolean',
            'wallet_balance' => 'decimal:2',
        ];
    }

    /**
     * Check if user can access Filament admin panel
     */
    public function canAccessPanel(Panel $panel): bool
    {
        return $this->role === 'admin' && $this->is_active;
    }

    /**
     * Check if user is admin
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }



    /**
     * Alias for cmsPages relationship (for statistics)
     */
    public function createdPages(): HasMany
    {
        return $this->cmsPages();
    }

    /**
     * Get CMS pages updated by this user
     */
    public function updatedCmsPages(): HasMany
    {
        return $this->hasMany(CmsPage::class, 'updated_by');
    }

    /**
     * Get email notifications for this user
     */
    public function emailNotifications(): HasMany
    {
        return $this->hasMany(EmailNotification::class);
    }

    /**
     * Get wallet transactions for this user
     */
    public function walletTransactions(): HasMany
    {
        return $this->hasMany(WalletTransaction::class);
    }

    /**
     * Securely update user role (admin only)
     */
    public function updateRole(string $role, User $updatedBy): bool
    {
        if (!$updatedBy->isAdmin()) {
            throw new \Exception('Only administrators can update user roles.');
        }

        $allowedRoles = ['user', 'admin'];
        if (!in_array($role, $allowedRoles)) {
            throw new \Exception('Invalid role specified.');
        }

        $this->role = $role;
        return $this->save();
    }

    /**
     * Securely update user active status (admin only)
     */
    public function updateActiveStatus(bool $isActive, User $updatedBy): bool
    {
        if (!$updatedBy->isAdmin()) {
            throw new \Exception('Only administrators can update user status.');
        }

        $this->is_active = $isActive;
        return $this->save();
    }

    /**
     * Securely update wallet balance (system only)
     */
    public function updateWalletBalance(float $amount, string $reason = null): bool
    {
        // This should only be called by the WalletTransactionService
        // Add logging for audit trail
        \Log::info('Wallet balance updated', [
            'user_id' => $this->id,
            'old_balance' => $this->wallet_balance,
            'amount_change' => $amount,
            'new_balance' => $this->wallet_balance + $amount,
            'reason' => $reason,
            'updated_at' => now(),
        ]);

        return $this->increment('wallet_balance', $amount);
    }

    /**
     * Legacy method for backward compatibility
     * @deprecated Use walletTransactions() instead
     */
    public function creditTransactions(): HasMany
    {
        return $this->walletTransactions();
    }

    /**
     * Add money to wallet balance
     */
    public function addToWallet(float $amount, string $description = null, string $type = 'bonus'): void
    {
        $this->increment('wallet_balance', $amount);

        // Create transaction record
        $this->walletTransactions()->create([
            'type' => $type,
            'amount' => $amount,
            'description' => $description ?? "Wallet credit: RM " . number_format($amount, 2),
            'payment_status' => 'completed',
            'processed_at' => now(),
        ]);
    }

    /**
     * Legacy method for backward compatibility
     * @deprecated Use addToWallet() instead
     */
    public function addCredits(int $amount, string $description = null): void
    {
        $this->addToWallet((float) $amount, $description, 'bonus');
    }

    /**
     * Deduct money from wallet balance
     */
    public function deductFromWallet(float $amount, string $description = null, string $type = 'payment'): bool
    {
        if ($this->wallet_balance < $amount) {
            return false;
        }

        $this->decrement('wallet_balance', $amount);

        // Create transaction record
        $this->walletTransactions()->create([
            'type' => $type,
            'amount' => -$amount,
            'description' => $description ?? "Wallet payment: RM " . number_format($amount, 2),
            'payment_status' => 'completed',
            'processed_at' => now(),
        ]);

        return true;
    }

    /**
     * Legacy method for backward compatibility
     * @deprecated Use deductFromWallet() instead
     */
    public function deductCredits(int $amount, string $description = null): bool
    {
        return $this->deductFromWallet((float) $amount, $description, 'payment');
    }

    /**
     * Check if user has enough money in wallet
     */
    public function hasWalletBalance(float $amount): bool
    {
        return $this->wallet_balance >= $amount;
    }

    /**
     * Get formatted wallet balance
     */
    public function getFormattedWalletBalance(): string
    {
        return 'RM ' . number_format($this->wallet_balance, 2);
    }

    /**
     * Legacy method for backward compatibility
     * @deprecated Use hasWalletBalance() instead
     */
    public function hasCredits(int $amount): bool
    {
        return $this->hasWalletBalance((float) $amount);
    }
}
