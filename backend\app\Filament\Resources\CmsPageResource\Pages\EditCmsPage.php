<?php

namespace App\Filament\Resources\CmsPageResource\Pages;

use App\Filament\Resources\CmsPageResource;
use App\Models\CmsPage;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;

class EditCmsPage extends EditRecord
{
    protected static string $resource = CmsPageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('preview')
                ->label('Preview')
                ->icon('heroicon-o-eye')
                ->color('success')
                ->url(fn (): string => url("/api/pages/{$this->record->slug}"))
                ->openUrlInNewTab()
                ->visible(fn (): bool => $this->record->is_published),

            Actions\Action::make('view_frontend')
                ->label('View on Site')
                ->icon('heroicon-o-globe-alt')
                ->color('info')
                ->url(fn (): string => url("/pages/{$this->record->slug}"))
                ->openUrlInNewTab()
                ->visible(fn (): bool => $this->record->is_published),

            Actions\Action::make('duplicate')
                ->label('Duplicate')
                ->icon('heroicon-o-document-duplicate')
                ->color('gray')
                ->requiresConfirmation()
                ->modalDescription('This will create a copy of this page.')
                ->action(function () {
                    $newPage = $this->record->replicate();
                    $newPage->title = $this->record->title . ' (Copy)';
                    $newPage->slug = $this->record->slug . '-copy-' . time();
                    $newPage->is_published = false;
                    $newPage->is_featured = false;
                    $newPage->published_at = null;
                    $newPage->created_by = auth()->id();
                    $newPage->updated_by = auth()->id();
                    $newPage->save();

                    Notification::make()
                        ->title('Page duplicated successfully')
                        ->success()
                        ->send();

                    return redirect($this->getResource()::getUrl('edit', ['record' => $newPage]));
                }),

            Actions\DeleteAction::make()
                ->requiresConfirmation()
                ->modalDescription('This action cannot be undone. All content will be permanently deleted.'),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Page updated successfully')
            ->body('The CMS page has been updated with your changes.');
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        $data['updated_by'] = auth()->id();

        // Handle content field based on editor type
        if (($data['editor_type'] ?? 'html') === 'puck') {
            // For Puck editor, ensure content is empty/null
            $data['content'] = null;
        } else {
            // For HTML editor, ensure content has a value (empty string if not provided)
            $data['content'] = $data['content'] ?? '';
        }

        // Handle publishing logic
        $wasPublished = $this->record->is_published;
        $isPublishing = $data['is_published'] ?? false;

        if ($isPublishing && !$wasPublished && empty($data['published_at'])) {
            // First time publishing - set published_at
            $data['published_at'] = now();
        } elseif (!$isPublishing && $wasPublished) {
            // Unpublishing - clear published_at
            $data['published_at'] = null;
        }

        return $data;
    }

    protected function afterSave(): void
    {
        $page = $this->record;

        // Log the update
        \Log::info('CMS page updated', [
            'page_id' => $page->id,
            'page_title' => $page->title,
            'page_slug' => $page->slug,
            'editor_type' => $page->editor_type,
            'is_published' => $page->is_published,
            'updated_by' => auth()->id(),
            'updated_by_name' => auth()->user()->name,
        ]);
    }
}
