[{"C:\\laragon\\www\\frontend\\src\\index.tsx": "1", "C:\\laragon\\www\\frontend\\src\\reportWebVitals.ts": "2", "C:\\laragon\\www\\frontend\\src\\App.tsx": "3", "C:\\laragon\\www\\frontend\\src\\utils\\errorHandlers.ts": "4", "C:\\laragon\\www\\frontend\\src\\contexts\\SettingsContext.tsx": "5", "C:\\laragon\\www\\frontend\\src\\contexts\\AuthContext.tsx": "6", "C:\\laragon\\www\\frontend\\src\\components\\common\\DynamicHead.tsx": "7", "C:\\laragon\\www\\frontend\\src\\components\\auth\\ProtectedRoute.tsx": "8", "C:\\laragon\\www\\frontend\\src\\components\\puck\\PuckEditor.tsx": "9", "C:\\laragon\\www\\frontend\\src\\components\\layout\\Navbar.tsx": "10", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DashboardRoute.tsx": "11", "C:\\laragon\\www\\frontend\\src\\components\\auth\\EmailVerificationNotice.tsx": "12", "C:\\laragon\\www\\frontend\\src\\pages\\auth\\ResetPassword.tsx": "13", "C:\\laragon\\www\\frontend\\src\\pages\\user\\Profile.tsx": "14", "C:\\laragon\\www\\frontend\\src\\pages\\auth\\ForgotPassword.tsx": "15", "C:\\laragon\\www\\frontend\\src\\pages\\auth\\Login.tsx": "16", "C:\\laragon\\www\\frontend\\src\\pages\\user\\EditProfile.tsx": "17", "C:\\laragon\\www\\frontend\\src\\pages\\auth\\Register.tsx": "18", "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Dashboard.tsx": "19", "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Orders.tsx": "20", "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Order.tsx": "21", "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Wallet.tsx": "22", "C:\\laragon\\www\\frontend\\src\\services\\settingsService.ts": "23", "C:\\laragon\\www\\frontend\\src\\services\\authService.ts": "24", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleLayout.tsx": "25", "C:\\laragon\\www\\frontend\\src\\components\\puck\\PuckRenderer.tsx": "26", "C:\\laragon\\www\\frontend\\src\\services\\printingService.ts": "27", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\ChartExample.tsx": "28", "C:\\laragon\\www\\frontend\\src\\services\\creditService.ts": "29", "C:\\laragon\\www\\frontend\\src\\components\\common\\Toast.tsx": "30", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\FileHistory.tsx": "31", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\FileUpload.tsx": "32", "C:\\laragon\\www\\frontend\\src\\components\\common\\ErrorBoundary.tsx": "33", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\FileReUpload.tsx": "34", "C:\\laragon\\www\\frontend\\src\\theme\\dattaAbleTheme.js": "35", "C:\\laragon\\www\\frontend\\src\\components\\wallet\\WalletTopUp.tsx": "36", "C:\\laragon\\www\\frontend\\src\\components\\wallet\\WalletBalance.tsx": "37", "C:\\laragon\\www\\frontend\\src\\components\\wallet\\WalletTransactionHistory.tsx": "38", "C:\\laragon\\www\\frontend\\src\\services\\api.ts": "39", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleFooter.tsx": "40", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleBreadcrumbs.tsx": "41", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleSidebar.tsx": "42", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleHeader.tsx": "43", "C:\\laragon\\www\\frontend\\src\\utils\\navigationCleanup.ts": "44", "C:\\laragon\\www\\frontend\\src\\utils\\navigationDiagnostics.ts": "45", "C:\\laragon\\www\\frontend\\src\\hooks\\useCleanNavigation.ts": "46", "C:\\laragon\\www\\frontend\\src\\utils\\strictModeWorkaround.ts": "47", "C:\\laragon\\www\\frontend\\src\\pages\\NotFound.tsx": "48", "C:\\laragon\\www\\frontend\\src\\components\\seo\\SEOHead.tsx": "49", "C:\\laragon\\www\\frontend\\src\\pages\\Home.tsx": "50"}, {"size": 554, "mtime": 1752503120783, "results": "51", "hashOfConfig": "52"}, {"size": 425, "mtime": 1752503120799, "results": "53", "hashOfConfig": "52"}, {"size": 5233, "mtime": 1753517284537, "results": "54", "hashOfConfig": "52"}, {"size": 5218, "mtime": 1753280345117, "results": "55", "hashOfConfig": "52"}, {"size": 4306, "mtime": 1753137950997, "results": "56", "hashOfConfig": "52"}, {"size": 4264, "mtime": 1752969422664, "results": "57", "hashOfConfig": "52"}, {"size": 2270, "mtime": 1753105704198, "results": "58", "hashOfConfig": "52"}, {"size": 1355, "mtime": 1752503120861, "results": "59", "hashOfConfig": "52"}, {"size": 27901, "mtime": 1753517219478, "results": "60", "hashOfConfig": "52"}, {"size": 4076, "mtime": 1753105850909, "results": "61", "hashOfConfig": "52"}, {"size": 430, "mtime": 1752856061163, "results": "62", "hashOfConfig": "52"}, {"size": 3494, "mtime": 1752503120861, "results": "63", "hashOfConfig": "52"}, {"size": 5805, "mtime": 1752503120814, "results": "64", "hashOfConfig": "52"}, {"size": 4077, "mtime": 1752504117348, "results": "65", "hashOfConfig": "52"}, {"size": 3447, "mtime": 1752503120814, "results": "66", "hashOfConfig": "52"}, {"size": 4586, "mtime": 1752969325386, "results": "67", "hashOfConfig": "52"}, {"size": 11533, "mtime": 1752504965088, "results": "68", "hashOfConfig": "52"}, {"size": 7895, "mtime": 1752969474297, "results": "69", "hashOfConfig": "52"}, {"size": 10483, "mtime": 1753517350528, "results": "70", "hashOfConfig": "52"}, {"size": 50676, "mtime": 1753057376603, "results": "71", "hashOfConfig": "52"}, {"size": 41159, "mtime": 1753055376399, "results": "72", "hashOfConfig": "52"}, {"size": 7813, "mtime": 1753312885093, "results": "73", "hashOfConfig": "52"}, {"size": 5490, "mtime": 1753137950932, "results": "74", "hashOfConfig": "52"}, {"size": 6824, "mtime": 1753201331566, "results": "75", "hashOfConfig": "52"}, {"size": 20289, "mtime": 1753315336502, "results": "76", "hashOfConfig": "52"}, {"size": 3884, "mtime": 1753199110606, "results": "77", "hashOfConfig": "52"}, {"size": 12488, "mtime": 1753024608606, "results": "78", "hashOfConfig": "52"}, {"size": 4729, "mtime": 1752536669944, "results": "79", "hashOfConfig": "52"}, {"size": 5582, "mtime": 1752847027187, "results": "80", "hashOfConfig": "52"}, {"size": 675, "mtime": 1753025888572, "results": "81", "hashOfConfig": "52"}, {"size": 8243, "mtime": 1753051474206, "results": "82", "hashOfConfig": "52"}, {"size": 29255, "mtime": 1753018297878, "results": "83", "hashOfConfig": "52"}, {"size": 1430, "mtime": 1752673572781, "results": "84", "hashOfConfig": "52"}, {"size": 10466, "mtime": 1753053921568, "results": "85", "hashOfConfig": "52"}, {"size": 5454, "mtime": 1753314619164, "results": "86", "hashOfConfig": "52"}, {"size": 11687, "mtime": 1752988950816, "results": "87", "hashOfConfig": "52"}, {"size": 11578, "mtime": 1753315352687, "results": "88", "hashOfConfig": "52"}, {"size": 10996, "mtime": 1752856638130, "results": "89", "hashOfConfig": "52"}, {"size": 2021, "mtime": 1753516973461, "results": "90", "hashOfConfig": "52"}, {"size": 2939, "mtime": 1752883952010, "results": "91", "hashOfConfig": "52"}, {"size": 2706, "mtime": 1752922816616, "results": "92", "hashOfConfig": "52"}, {"size": 7938, "mtime": 1753105982975, "results": "93", "hashOfConfig": "52"}, {"size": 10352, "mtime": 1752970126996, "results": "94", "hashOfConfig": "52"}, {"size": 5193, "mtime": 1753017078083, "results": "95", "hashOfConfig": "52"}, {"size": 7310, "mtime": 1753017533626, "results": "96", "hashOfConfig": "52"}, {"size": 3019, "mtime": 1753017058753, "results": "97", "hashOfConfig": "52"}, {"size": 2543, "mtime": 1753003973465, "results": "98", "hashOfConfig": "52"}, {"size": 4309, "mtime": 1753487007603, "results": "99", "hashOfConfig": "52"}, {"size": 3386, "mtime": 1753487070396, "results": "100", "hashOfConfig": "52"}, {"size": 3715, "mtime": 1753516802930, "results": "101", "hashOfConfig": "52"}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "tace3p", {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\laragon\\www\\frontend\\src\\index.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\laragon\\www\\frontend\\src\\App.tsx", ["252"], [], "C:\\laragon\\www\\frontend\\src\\utils\\errorHandlers.ts", [], [], "C:\\laragon\\www\\frontend\\src\\contexts\\SettingsContext.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\contexts\\AuthContext.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\common\\DynamicHead.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\auth\\ProtectedRoute.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\puck\\PuckEditor.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\layout\\Navbar.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DashboardRoute.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\auth\\EmailVerificationNotice.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\auth\\ResetPassword.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\user\\Profile.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\auth\\ForgotPassword.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\auth\\Login.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\user\\EditProfile.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\auth\\Register.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Dashboard.tsx", ["253"], [], "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Orders.tsx", ["254", "255", "256", "257", "258", "259", "260", "261", "262", "263", "264", "265"], [], "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Order.tsx", ["266", "267", "268", "269", "270", "271"], [], "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Wallet.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\services\\settingsService.ts", [], [], "C:\\laragon\\www\\frontend\\src\\services\\authService.ts", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleLayout.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\puck\\PuckRenderer.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\services\\printingService.ts", ["272"], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\ChartExample.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\services\\creditService.ts", ["273", "274"], [], "C:\\laragon\\www\\frontend\\src\\components\\common\\Toast.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\FileHistory.tsx", ["275"], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\FileUpload.tsx", ["276", "277"], [], "C:\\laragon\\www\\frontend\\src\\components\\common\\ErrorBoundary.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\FileReUpload.tsx", ["278"], [], "C:\\laragon\\www\\frontend\\src\\theme\\dattaAbleTheme.js", [], [], "C:\\laragon\\www\\frontend\\src\\components\\wallet\\WalletTopUp.tsx", ["279", "280", "281"], [], "C:\\laragon\\www\\frontend\\src\\components\\wallet\\WalletBalance.tsx", ["282"], [], "C:\\laragon\\www\\frontend\\src\\components\\wallet\\WalletTransactionHistory.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\services\\api.ts", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleFooter.tsx", ["283", "284", "285", "286"], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleBreadcrumbs.tsx", ["287", "288"], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleSidebar.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleHeader.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\utils\\navigationCleanup.ts", [], [], "C:\\laragon\\www\\frontend\\src\\utils\\navigationDiagnostics.ts", [], [], "C:\\laragon\\www\\frontend\\src\\hooks\\useCleanNavigation.ts", [], [], "C:\\laragon\\www\\frontend\\src\\utils\\strictModeWorkaround.ts", ["289"], [], "C:\\laragon\\www\\frontend\\src\\pages\\NotFound.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\seo\\SEOHead.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\Home.tsx", ["290"], [], {"ruleId": "291", "severity": 2, "message": "292", "line": 145, "column": 47, "nodeType": "293", "messageId": "294", "endLine": 145, "endColumn": 66}, {"ruleId": "295", "severity": 1, "message": "296", "line": 8, "column": 10, "nodeType": "297", "messageId": "298", "endLine": 8, "endColumn": 14}, {"ruleId": "295", "severity": 1, "message": "299", "line": 27, "column": 3, "nodeType": "297", "messageId": "298", "endLine": 27, "endColumn": 10}, {"ruleId": "295", "severity": 1, "message": "300", "line": 32, "column": 3, "nodeType": "297", "messageId": "298", "endLine": 32, "endColumn": 19}, {"ruleId": "295", "severity": 1, "message": "301", "line": 40, "column": 3, "nodeType": "297", "messageId": "298", "endLine": 40, "endColumn": 9}, {"ruleId": "295", "severity": 1, "message": "302", "line": 41, "column": 3, "nodeType": "297", "messageId": "298", "endLine": 41, "endColumn": 10}, {"ruleId": "295", "severity": 1, "message": "303", "line": 42, "column": 3, "nodeType": "297", "messageId": "298", "endLine": 42, "endColumn": 7}, {"ruleId": "295", "severity": 1, "message": "304", "line": 43, "column": 3, "nodeType": "297", "messageId": "298", "endLine": 43, "endColumn": 6}, {"ruleId": "295", "severity": 1, "message": "305", "line": 44, "column": 3, "nodeType": "297", "messageId": "298", "endLine": 44, "endColumn": 8}, {"ruleId": "295", "severity": 1, "message": "306", "line": 53, "column": 17, "nodeType": "297", "messageId": "298", "endLine": 53, "endColumn": 27}, {"ruleId": "295", "severity": 1, "message": "307", "line": 79, "column": 9, "nodeType": "297", "messageId": "298", "endLine": 79, "endColumn": 17}, {"ruleId": "295", "severity": 1, "message": "308", "line": 104, "column": 10, "nodeType": "297", "messageId": "298", "endLine": 104, "endColumn": 19}, {"ruleId": "295", "severity": 1, "message": "309", "line": 104, "column": 21, "nodeType": "297", "messageId": "298", "endLine": 104, "endColumn": 33}, {"ruleId": "310", "severity": 1, "message": "311", "line": 114, "column": 6, "nodeType": "312", "endLine": 114, "endColumn": 12, "suggestions": "313"}, {"ruleId": "295", "severity": 1, "message": "314", "line": 18, "column": 3, "nodeType": "297", "messageId": "298", "endLine": 18, "endColumn": 14}, {"ruleId": "295", "severity": 1, "message": "315", "line": 19, "column": 3, "nodeType": "297", "messageId": "298", "endLine": 19, "endColumn": 13}, {"ruleId": "295", "severity": 1, "message": "316", "line": 20, "column": 3, "nodeType": "297", "messageId": "298", "endLine": 20, "endColumn": 9}, {"ruleId": "295", "severity": 1, "message": "317", "line": 21, "column": 3, "nodeType": "297", "messageId": "298", "endLine": 21, "endColumn": 11}, {"ruleId": "295", "severity": 1, "message": "318", "line": 48, "column": 25, "nodeType": "297", "messageId": "298", "endLine": 48, "endColumn": 41}, {"ruleId": "295", "severity": 1, "message": "319", "line": 58, "column": 10, "nodeType": "297", "messageId": "298", "endLine": 58, "endColumn": 22}, {"ruleId": "320", "severity": 1, "message": "321", "line": 413, "column": 1, "nodeType": "322", "endLine": 413, "endColumn": 38}, {"ruleId": "295", "severity": 1, "message": "323", "line": 1, "column": 15, "nodeType": "297", "messageId": "298", "endLine": 1, "endColumn": 24}, {"ruleId": "320", "severity": 1, "message": "321", "line": 223, "column": 1, "nodeType": "322", "endLine": 223, "endColumn": 36}, {"ruleId": "310", "severity": 1, "message": "324", "line": 58, "column": 6, "nodeType": "312", "endLine": 58, "endColumn": 30, "suggestions": "325"}, {"ruleId": "310", "severity": 1, "message": "326", "line": 91, "column": 6, "nodeType": "312", "endLine": 91, "endColumn": 15, "suggestions": "327"}, {"ruleId": "310", "severity": 1, "message": "328", "line": 280, "column": 6, "nodeType": "312", "endLine": 280, "endColumn": 49, "suggestions": "329"}, {"ruleId": "295", "severity": 1, "message": "330", "line": 48, "column": 10, "nodeType": "297", "messageId": "298", "endLine": 48, "endColumn": 20}, {"ruleId": "295", "severity": 1, "message": "331", "line": 3, "column": 3, "nodeType": "297", "messageId": "298", "endLine": 3, "endColumn": 12}, {"ruleId": "295", "severity": 1, "message": "305", "line": 11, "column": 3, "nodeType": "297", "messageId": "298", "endLine": 11, "endColumn": 8}, {"ruleId": "295", "severity": 1, "message": "332", "line": 111, "column": 9, "nodeType": "297", "messageId": "298", "endLine": 111, "endColumn": 19}, {"ruleId": "295", "severity": 1, "message": "331", "line": 3, "column": 3, "nodeType": "297", "messageId": "298", "endLine": 3, "endColumn": 12}, {"ruleId": "333", "severity": 1, "message": "334", "line": 33, "column": 15, "nodeType": "335", "endLine": 42, "endColumn": 16}, {"ruleId": "333", "severity": 1, "message": "334", "line": 49, "column": 15, "nodeType": "335", "endLine": 59, "endColumn": 16}, {"ruleId": "333", "severity": 1, "message": "334", "line": 62, "column": 15, "nodeType": "335", "endLine": 72, "endColumn": 16}, {"ruleId": "333", "severity": 1, "message": "334", "line": 75, "column": 15, "nodeType": "335", "endLine": 84, "endColumn": 16}, {"ruleId": "295", "severity": 1, "message": "336", "line": 19, "column": 11, "nodeType": "297", "messageId": "298", "endLine": 19, "endColumn": 23}, {"ruleId": "295", "severity": 1, "message": "337", "line": 51, "column": 9, "nodeType": "297", "messageId": "298", "endLine": 51, "endColumn": 29}, {"ruleId": "320", "severity": 1, "message": "338", "line": 91, "column": 1, "nodeType": "322", "endLine": 97, "endColumn": 3}, {"ruleId": null, "fatal": true, "severity": 2, "message": "339", "line": 117, "column": 0, "nodeType": null}, "react/jsx-no-undef", "'DynamicRouteHandler' is not defined.", "JSXIdentifier", "undefined", "@typescript-eslint/no-unused-vars", "'Link' is defined but never used.", "Identifier", "unusedVar", "'Divider' is defined but never used.", "'CircularProgress' is defined but never used.", "'AppBar' is defined but never used.", "'Toolbar' is defined but never used.", "'Tabs' is defined but never used.", "'Tab' is defined but never used.", "'Badge' is defined but never used.", "'FilterIcon' is defined but never used.", "'isTablet' is assigned a value but never used.", "'activeTab' is assigned a value but never used.", "'setActiveTab' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadOrders'. Either include it or remove the dependency array.", "ArrayExpression", ["340"], "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'setCreatingOrder' is assigned a value but never used.", "'createdOrder' is assigned a value but never used.", "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration", "'endpoints' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadHistory'. Either include it or remove the dependency array.", ["341"], "React Hook useEffect has missing dependencies: 'loadSettings' and 'loadUploadedFiles'. Either include them or remove the dependency array.", ["342"], "React Hook useCallback has missing dependencies: 'uploadFiles' and 'validateFiles'. Either include them or remove the dependency array.", ["343"], "'dragActive' is assigned a value but never used.", "'Container' is defined but never used.", "'newBalance' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'pathSegments' is assigned a value but never used.", "'breadcrumbItemStyles' is assigned a value but never used.", "Assign object to a variable before exporting as module default", "Parsing error: '}' expected.", {"desc": "344", "fix": "345"}, {"desc": "346", "fix": "347"}, {"desc": "348", "fix": "349"}, {"desc": "350", "fix": "351"}, "Update the dependencies array to be: [loadOrders, page]", {"range": "352", "text": "353"}, "Update the dependencies array to be: [open, orderId, file.id, loadHistory]", {"range": "354", "text": "355"}, "Update the dependencies array to be: [loadSettings, loadUploadedFiles, orderId]", {"range": "356", "text": "357"}, "Update the dependencies array to be: [orderId, validateFiles, onError, uploadFiles]", {"range": "358", "text": "359"}, [3415, 3421], "[loadOrders, page]", [1158, 1182], "[open, orderId, file.id, loadHistory]", [2685, 2694], "[loadSettings, loadUploadedFiles, orderId]", [9503, 9546], "[orderId, validateFiles, onError, uploadFiles]"]