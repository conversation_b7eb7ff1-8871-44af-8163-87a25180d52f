<?php

namespace App\Policies;

use App\Models\CmsPage;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class CmsPagePolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, CmsPage $cmsPage): bool
    {
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, CmsPage $cmsPage): bool
    {
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, CmsPage $cmsPage): bool
    {
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, CmsPage $cmsPage): bool
    {
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, CmsPage $cmsPage): bool
    {
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can publish/unpublish the model.
     */
    public function publish(User $user, CmsPage $cmsPage): bool
    {
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can feature/unfeature the model.
     */
    public function feature(User $user, CmsPage $cmsPage): bool
    {
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can duplicate the model.
     */
    public function duplicate(User $user, CmsPage $cmsPage): bool
    {
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can bulk delete models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can bulk publish models.
     */
    public function publishAny(User $user): bool
    {
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can bulk unpublish models.
     */
    public function unpublishAny(User $user): bool
    {
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can bulk feature models.
     */
    public function featureAny(User $user): bool
    {
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can bulk unfeature models.
     */
    public function unfeatureAny(User $user): bool
    {
        return $user->role === 'admin';
    }
}
