<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cms_pages', function (Blueprint $table) {
            // Add status field with enum for better page state management
            $table->enum('status', ['draft', 'published', 'archived'])
                ->default('draft')
                ->after('is_published')
                ->comment('Page status: draft, published, or archived');

            // Add template field for different page layouts
            $table->string('template', 50)
                ->default('default')
                ->after('status')
                ->comment('Page template/layout identifier');

            // Add SEO fields
            $table->string('meta_title', 60)
                ->nullable()
                ->after('meta_keywords')
                ->comment('SEO title (max 60 chars for optimal display)');

            $table->text('meta_robots')
                ->nullable()
                ->after('meta_title')
                ->comment('Meta robots directive (index, noindex, follow, nofollow, etc.)');

            $table->string('canonical_url')
                ->nullable()
                ->after('meta_robots')
                ->comment('Canonical URL for SEO');

            // Add social media fields
            $table->string('og_title')
                ->nullable()
                ->after('canonical_url')
                ->comment('Open Graph title for social media sharing');

            $table->text('og_description')
                ->nullable()
                ->after('og_title')
                ->comment('Open Graph description for social media sharing');

            $table->string('og_image')
                ->nullable()
                ->after('og_description')
                ->comment('Open Graph image URL for social media sharing');

            // Add content management fields
            $table->integer('sort_order')
                ->default(0)
                ->after('og_image')
                ->comment('Sort order for page listing');

            $table->json('page_settings')
                ->nullable()
                ->after('sort_order')
                ->comment('Additional page settings and configuration');

            // Add scheduling fields
            $table->timestamp('scheduled_publish_at')
                ->nullable()
                ->after('published_at')
                ->comment('Scheduled publication date/time');

            $table->timestamp('scheduled_unpublish_at')
                ->nullable()
                ->after('scheduled_publish_at')
                ->comment('Scheduled unpublication date/time');

            // Add indexes for better performance
            $table->index('status');
            $table->index('template');
            $table->index('sort_order');
            $table->index('scheduled_publish_at');
            $table->index(['status', 'published_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cms_pages', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['status', 'published_at']);
            $table->dropIndex(['scheduled_publish_at']);
            $table->dropIndex(['sort_order']);
            $table->dropIndex(['template']);
            $table->dropIndex(['status']);

            // Drop columns
            $table->dropColumn([
                'status',
                'template',
                'meta_title',
                'meta_robots',
                'canonical_url',
                'og_title',
                'og_description',
                'og_image',
                'sort_order',
                'page_settings',
                'scheduled_publish_at',
                'scheduled_unpublish_at',
            ]);
        });
    }
};
