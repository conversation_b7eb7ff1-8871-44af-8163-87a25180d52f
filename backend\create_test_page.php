<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

// Get the first user
$user = App\Models\User::first();

if (!$user) {
    echo "No users found. Please create a user first.\n";
    exit(1);
}

// Create a test page
try {
    $page = App\Models\CmsPage::create([
        'title' => 'Welcome to Our Dynamic CMS',
        'slug' => 'welcome-dynamic-cms',
        'content' => '<h1>Welcome to Our Dynamic CMS</h1><p>This is a test page created to demonstrate our new dynamic CMS system with enhanced SEO capabilities, multiple templates, and comprehensive content management features.</p><p>This page uses the <strong>default template</strong> and includes all the new functionality we\'ve built.</p><h2>Features Demonstrated</h2><ul><li>Dynamic routing</li><li>SEO optimization</li><li>Multiple templates</li><li>Enhanced admin panel</li><li>Comprehensive validation</li></ul>',
        'editor_type' => 'html',
        'template' => 'default',
        'status' => 'published',
        'meta_title' => 'Welcome to Our Dynamic CMS - Test Page',
        'meta_description' => 'A comprehensive test page showcasing our new dynamic CMS system with enhanced SEO, multiple templates, and advanced content management.',
        'meta_keywords' => 'cms, dynamic, test, seo, templates',
        'og_title' => 'Dynamic CMS Test Page',
        'og_description' => 'Experience our new dynamic CMS system with this comprehensive test page.',
        'is_published' => true,
        'is_featured' => true,
        'sort_order' => 1,
        'created_by' => $user->id,
        'updated_by' => $user->id,
        'published_at' => now(),
    ]);

    echo "✅ Successfully created test page:\n";
    echo "   Title: {$page->title}\n";
    echo "   Slug: {$page->slug}\n";
    echo "   ID: {$page->id}\n";
    echo "   Template: {$page->template}\n";
    echo "   Status: {$page->status}\n";
    echo "   URL: http://localhost:3000/pages/{$page->slug}\n";

} catch (Exception $e) {
    echo "❌ Error creating test page: " . $e->getMessage() . "\n";
    exit(1);
}

// Create a blog post template test page
try {
    $blogPage = App\Models\CmsPage::create([
        'title' => 'Building a Modern CMS with Laravel and React',
        'slug' => 'building-modern-cms-laravel-react',
        'content' => '<p>In this comprehensive guide, we\'ll explore how to build a modern, dynamic CMS using Laravel for the backend and React for the frontend.</p><h2>Why Choose Laravel and React?</h2><p>Laravel provides a robust, secure backend framework with excellent ORM capabilities, while React offers a dynamic, component-based frontend that can create engaging user experiences.</p><h2>Key Features We\'ve Implemented</h2><ul><li><strong>Dynamic Routing:</strong> Pages created in the admin panel are automatically available on the frontend</li><li><strong>SEO Optimization:</strong> Comprehensive meta tags, Open Graph support, and structured data</li><li><strong>Multiple Templates:</strong> Different layouts for different content types</li><li><strong>Security:</strong> Input validation, CORS protection, and authorization</li></ul><h2>Technical Architecture</h2><p>Our CMS follows a modern architecture pattern with clear separation of concerns between the backend API and frontend application.</p>',
        'editor_type' => 'html',
        'template' => 'blog',
        'status' => 'published',
        'meta_title' => 'Building a Modern CMS with Laravel and React',
        'meta_description' => 'Learn how to build a comprehensive CMS system using Laravel backend and React frontend with dynamic routing, SEO optimization, and multiple templates.',
        'meta_keywords' => 'laravel, react, cms, tutorial, web development, php, javascript',
        'og_title' => 'Building a Modern CMS: Laravel + React Tutorial',
        'og_description' => 'Complete guide to building a dynamic CMS with Laravel and React, featuring SEO optimization and multiple templates.',
        'is_published' => true,
        'is_featured' => false,
        'sort_order' => 2,
        'created_by' => $user->id,
        'updated_by' => $user->id,
        'published_at' => now(),
    ]);

    echo "\n✅ Successfully created blog post page:\n";
    echo "   Title: {$blogPage->title}\n";
    echo "   Slug: {$blogPage->slug}\n";
    echo "   ID: {$blogPage->id}\n";
    echo "   Template: {$blogPage->template}\n";
    echo "   URL: http://localhost:3000/pages/{$blogPage->slug}\n";

} catch (Exception $e) {
    echo "❌ Error creating blog page: " . $e->getMessage() . "\n";
}

echo "\n🎉 Test pages created successfully!\n";
echo "You can now test the dynamic CMS system by visiting the URLs above.\n";
