<?php

namespace App\Filament\Resources\CmsPageResource\Pages;

use App\Filament\Resources\CmsPageResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;

class CreateCmsPage extends CreateRecord
{
    protected static string $resource = CmsPageResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Page created successfully')
            ->body('The CMS page has been created and is ready for content.');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['created_by'] = auth()->id();
        $data['updated_by'] = auth()->id();

        // Handle content field based on editor type
        if (($data['editor_type'] ?? 'html') === 'puck') {
            // For Puck editor, ensure content is empty/null
            $data['content'] = null;
        } else {
            // For HTML editor, ensure content has a value (empty string if not provided)
            $data['content'] = $data['content'] ?? '';
        }

        // Set published_at if publishing
        if (($data['is_published'] ?? false) && empty($data['published_at'])) {
            $data['published_at'] = now();
        }

        return $data;
    }

    protected function afterCreate(): void
    {
        $page = $this->record;

        // Log the creation
        \Log::info('CMS page created', [
            'page_id' => $page->id,
            'page_title' => $page->title,
            'page_slug' => $page->slug,
            'editor_type' => $page->editor_type,
            'is_published' => $page->is_published,
            'created_by' => auth()->id(),
            'created_by_name' => auth()->user()->name,
        ]);
    }
}
