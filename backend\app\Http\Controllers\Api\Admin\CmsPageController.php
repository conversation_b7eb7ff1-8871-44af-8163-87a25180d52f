<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\CmsPage;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use App\Services\PuckValidationService;
use App\Services\PuckContentTransformationService;

class CmsPageController extends Controller
{
    private PuckValidationService $puckValidation;
    private PuckContentTransformationService $contentTransformation;

    public function __construct(
        PuckValidationService $puckValidation,
        PuckContentTransformationService $contentTransformation
    ) {
        $this->puckValidation = $puckValidation;
        $this->contentTransformation = $contentTransformation;
    }

    /**
     * Get all CMS pages (admin view - includes unpublished)
     */
    public function index(Request $request): JsonResponse
    {
        $query = CmsPage::with(['creator:id,name', 'updater:id,name'])
            ->orderBy('created_at', 'desc');

        // Filter by published status
        if ($request->has('published')) {
            $published = $request->boolean('published');
            $query->where('is_published', $published);
        }

        // Filter by featured status
        if ($request->has('featured')) {
            $featured = $request->boolean('featured');
            $query->where('is_featured', $featured);
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%")
                  ->orWhere('slug', 'like', "%{$search}%")
                  ->orWhere('meta_description', 'like', "%{$search}%")
                  ->orWhere('meta_keywords', 'like', "%{$search}%");
            });
        }

        // Filter by creator
        if ($request->filled('created_by')) {
            $query->where('created_by', $request->get('created_by'));
        }

        $perPage = $request->get('per_page', 15);
        $pages = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => [
                'pages' => $pages->items(),
                'pagination' => [
                    'current_page' => $pages->currentPage(),
                    'last_page' => $pages->lastPage(),
                    'per_page' => $pages->perPage(),
                    'total' => $pages->total(),
                    'has_more_pages' => $pages->hasMorePages(),
                ]
            ]
        ]);
    }

    /**
     * Get a specific CMS page by ID (admin view)
     */
    public function show(int $id): JsonResponse
    {
        $page = CmsPage::with(['creator:id,name', 'updater:id,name'])
            ->find($id);

        if (!$page) {
            return response()->json([
                'success' => false,
                'message' => 'Page not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'page' => $page
            ]
        ]);
    }

    /**
     * Create a new CMS page
     */
    public function store(Request $request): JsonResponse
    {
        // Base validation rules
        $rules = [
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:cms_pages,slug|regex:/^[a-z0-9-]+$/',
            'meta_description' => 'nullable|string|max:160',
            'meta_keywords' => 'nullable|string|max:255',
            'is_published' => 'boolean',
            'is_featured' => 'boolean',
            'featured_image' => 'nullable|string|max:255',
            'published_at' => 'nullable|date',
            'editor_type' => 'required|in:html,puck',
        ];

        // Add content validation based on editor type
        $editorType = $request->input('editor_type', 'html');
        if ($editorType === 'puck') {
            $rules = array_merge($rules, $this->puckValidation->getPuckValidationRules());
            $rules['puck_meta'] = 'sometimes|array';
        } else {
            $rules['content'] = 'required|string';
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();

        // Handle Puck-specific processing
        if ($editorType === 'puck') {
            try {
                // Validate and ensure component IDs
                $data['puck_data'] = $this->puckValidation->validateAndEnsureIds($data['puck_data']);

                // Generate rendered content from Puck data
                $data['rendered_content'] = $this->contentTransformation->puckToHtml($data['puck_data']);

                // Extract SEO data from Puck content if not provided
                $seoData = $this->contentTransformation->extractSeoData($data['puck_data']);
                if (empty($data['meta_description']) && !empty($seoData['meta_description'])) {
                    $data['meta_description'] = $seoData['meta_description'];
                }

                // Set content as rendered HTML for backward compatibility
                $data['content'] = $data['rendered_content'];

            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid Puck data: ' . $e->getMessage()
                ], 422);
            }
        }

        // Auto-generate slug if not provided
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['title']);

            // Ensure slug is unique
            $originalSlug = $data['slug'];
            $counter = 1;
            while (CmsPage::where('slug', $data['slug'])->exists()) {
                $data['slug'] = $originalSlug . '-' . $counter;
                $counter++;
            }
        }

        // Set creator
        $data['created_by'] = $request->user()->id;
        $data['updated_by'] = $request->user()->id;

        // Set published_at if publishing
        if (($data['is_published'] ?? false) && empty($data['published_at'])) {
            $data['published_at'] = now();
        }

        try {
            $page = CmsPage::create($data);
            $page->load(['creator:id,name', 'updater:id,name']);

            return response()->json([
                'success' => true,
                'message' => 'Page created successfully',
                'data' => [
                    'page' => $page
                ]
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create page: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update an existing CMS page
     */
    public function update(Request $request, int $id): JsonResponse
    {
        $page = CmsPage::find($id);

        if (!$page) {
            return response()->json([
                'success' => false,
                'message' => 'Page not found'
            ], 404);
        }

        // Base validation rules
        $rules = [
            'title' => 'required|string|max:255',
            'slug' => [
                'nullable',
                'string',
                'max:255',
                'regex:/^[a-z0-9-]+$/',
                Rule::unique('cms_pages', 'slug')->ignore($id)
            ],
            'meta_description' => 'nullable|string|max:160',
            'meta_keywords' => 'nullable|string|max:255',
            'is_published' => 'boolean',
            'is_featured' => 'boolean',
            'featured_image' => 'nullable|string|max:255',
            'published_at' => 'nullable|date',
            'editor_type' => 'sometimes|in:html,puck',
        ];

        // Determine editor type (use existing if not provided)
        $editorType = $request->input('editor_type', $page->editor_type);

        // Add content validation based on editor type
        if ($editorType === 'puck') {
            $rules = array_merge($rules, $this->puckValidation->getPuckValidationRules());
            $rules['puck_meta'] = 'sometimes|array';
        } else {
            $rules['content'] = 'required|string';
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();

        // Handle editor type switching
        if (isset($data['editor_type']) && $data['editor_type'] !== $page->editor_type) {
            if ($data['editor_type'] === 'puck' && $page->editor_type === 'html') {
                // Convert HTML to Puck
                $data['puck_data'] = $this->contentTransformation->htmlToPuck(
                    $page->content,
                    [
                        'title' => $data['title'],
                        'meta_description' => $data['meta_description'] ?? '',
                        'meta_keywords' => $data['meta_keywords'] ?? '',
                    ]
                );
                $data['rendered_content'] = $page->content; // Keep original HTML
            } elseif ($data['editor_type'] === 'html' && $page->editor_type === 'puck') {
                // Convert Puck to HTML
                $data['content'] = $page->rendered_content ?: $this->contentTransformation->puckToHtml($page->puck_data ?: []);
                $data['puck_data'] = null;
                $data['rendered_content'] = null;
                $data['puck_meta'] = null;
            }
        }

        // Handle Puck-specific processing
        if ($editorType === 'puck' && isset($data['puck_data'])) {
            try {
                // Validate and ensure component IDs
                $data['puck_data'] = $this->puckValidation->validateAndEnsureIds($data['puck_data']);

                // Generate rendered content from Puck data
                $data['rendered_content'] = $this->contentTransformation->puckToHtml($data['puck_data']);

                // Extract SEO data from Puck content if not provided
                $seoData = $this->contentTransformation->extractSeoData($data['puck_data']);
                if (empty($data['meta_description']) && !empty($seoData['meta_description'])) {
                    $data['meta_description'] = $seoData['meta_description'];
                }

                // Set content as rendered HTML for backward compatibility
                $data['content'] = $data['rendered_content'];

            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid Puck data: ' . $e->getMessage()
                ], 422);
            }
        }

        // Auto-generate slug if not provided
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['title']);

            // Ensure slug is unique (excluding current page)
            $originalSlug = $data['slug'];
            $counter = 1;
            while (CmsPage::where('slug', $data['slug'])->where('id', '!=', $id)->exists()) {
                $data['slug'] = $originalSlug . '-' . $counter;
                $counter++;
            }
        }

        // Set updater
        $data['updated_by'] = $request->user()->id;

        // Handle publishing logic
        $wasPublished = $page->is_published;
        $isPublishing = $data['is_published'] ?? false;

        if ($isPublishing && !$wasPublished && empty($data['published_at'])) {
            // First time publishing - set published_at
            $data['published_at'] = now();
        } elseif (!$isPublishing && $wasPublished) {
            // Unpublishing - clear published_at
            $data['published_at'] = null;
        }

        try {
            $page->update($data);
            $page->load(['creator:id,name', 'updater:id,name']);

            return response()->json([
                'success' => true,
                'message' => 'Page updated successfully',
                'data' => [
                    'page' => $page
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update page: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a CMS page
     */
    public function destroy(int $id): JsonResponse
    {
        $page = CmsPage::find($id);

        if (!$page) {
            return response()->json([
                'success' => false,
                'message' => 'Page not found'
            ], 404);
        }

        try {
            $page->delete();

            return response()->json([
                'success' => true,
                'message' => 'Page deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete page: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle published status of a page
     */
    public function togglePublished(int $id): JsonResponse
    {
        $page = CmsPage::find($id);

        if (!$page) {
            return response()->json([
                'success' => false,
                'message' => 'Page not found'
            ], 404);
        }

        try {
            $newStatus = !$page->is_published;
            $updateData = [
                'is_published' => $newStatus,
                'updated_by' => auth()->id(),
            ];

            if ($newStatus && !$page->published_at) {
                // First time publishing
                $updateData['published_at'] = now();
            } elseif (!$newStatus) {
                // Unpublishing
                $updateData['published_at'] = null;
            }

            $page->update($updateData);
            $page->load(['creator:id,name', 'updater:id,name']);

            return response()->json([
                'success' => true,
                'message' => $newStatus ? 'Page published successfully' : 'Page unpublished successfully',
                'data' => [
                    'page' => $page
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to toggle page status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle featured status of a page
     */
    public function toggleFeatured(int $id): JsonResponse
    {
        $page = CmsPage::find($id);

        if (!$page) {
            return response()->json([
                'success' => false,
                'message' => 'Page not found'
            ], 404);
        }

        try {
            $newStatus = !$page->is_featured;
            $page->update([
                'is_featured' => $newStatus,
                'updated_by' => auth()->id(),
            ]);
            $page->load(['creator:id,name', 'updater:id,name']);

            return response()->json([
                'success' => true,
                'message' => $newStatus ? 'Page marked as featured' : 'Page removed from featured',
                'data' => [
                    'page' => $page
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to toggle featured status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get CMS statistics for admin dashboard
     */
    public function statistics(): JsonResponse
    {
        try {
            $stats = [
                'total_pages' => CmsPage::count(),
                'published_pages' => CmsPage::where('is_published', true)->count(),
                'unpublished_pages' => CmsPage::where('is_published', false)->count(),
                'featured_pages' => CmsPage::where('is_featured', true)->count(),
                'recent_pages' => CmsPage::where('created_at', '>=', now()->subDays(7))->count(),
                'puck_pages' => CmsPage::where('editor_type', 'puck')->count(),
                'html_pages' => CmsPage::where('editor_type', 'html')->count(),
            ];

            return response()->json([
                'success' => true,
                'data' => [
                    'statistics' => $stats
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch statistics: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get page in Puck format for the editor
     */
    public function getPuckData(int $id): JsonResponse
    {
        $page = CmsPage::find($id);

        if (!$page) {
            return response()->json([
                'success' => false,
                'message' => 'Page not found'
            ], 404);
        }

        try {
            if ($page->isPuckPage()) {
                $puckData = $page->puck_data ?: [];
            } else {
                // Convert HTML to Puck format
                $puckData = $this->contentTransformation->htmlToPuck(
                    $page->content,
                    [
                        'title' => $page->title,
                        'meta_description' => $page->meta_description,
                        'meta_keywords' => $page->meta_keywords,
                    ]
                );
            }

            // Ensure all components have IDs
            $puckData = $this->puckValidation->ensureComponentIds($puckData);

            return response()->json([
                'success' => true,
                'data' => [
                    'page' => $page,
                    'puck_data' => $puckData,
                    'editor_type' => $page->editor_type,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get Puck data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Save Puck data for a page
     */
    public function savePuckData(Request $request, int $id): JsonResponse
    {
        $page = CmsPage::find($id);

        if (!$page) {
            return response()->json([
                'success' => false,
                'message' => 'Page not found'
            ], 404);
        }

        $validator = Validator::make($request->all(), array_merge(
            $this->puckValidation->getPuckValidationRules(),
            [
                'puck_meta' => 'sometimes|array',
                'auto_save' => 'sometimes|boolean',
            ]
        ));

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $data = $validator->validated();

            // Validate and ensure component IDs
            $data['puck_data'] = $this->puckValidation->validateAndEnsureIds($data['puck_data']);

            // Generate rendered content
            $renderedContent = $this->contentTransformation->puckToHtml($data['puck_data']);

            // Extract SEO data
            $seoData = $this->contentTransformation->extractSeoData($data['puck_data']);

            // Update page
            $updateData = [
                'puck_data' => $data['puck_data'],
                'rendered_content' => $renderedContent,
                'content' => $renderedContent, // For backward compatibility
                'editor_type' => 'puck',
                'updated_by' => $request->user()->id,
            ];

            // Update SEO data if extracted from Puck content
            if (!empty($seoData['title']) && $seoData['title'] !== $page->title) {
                $updateData['title'] = $seoData['title'];
            }
            if (!empty($seoData['meta_description'])) {
                $updateData['meta_description'] = $seoData['meta_description'];
            }
            if (!empty($seoData['meta_keywords'])) {
                $updateData['meta_keywords'] = $seoData['meta_keywords'];
            }

            if (isset($data['puck_meta'])) {
                $updateData['puck_meta'] = $data['puck_meta'];
            }

            $page->update($updateData);
            $page->load(['creator:id,name', 'updater:id,name']);

            $isAutoSave = $request->boolean('auto_save', false);
            $message = $isAutoSave ? 'Page auto-saved successfully' : 'Puck data saved successfully';

            return response()->json([
                'success' => true,
                'message' => $message,
                'data' => [
                    'page' => $page,
                    'rendered_content' => $renderedContent,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to save Puck data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Convert HTML content to Puck format
     */
    public function convertHtmlToPuck(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'content' => 'required|string',
            'title' => 'sometimes|string',
            'meta_description' => 'sometimes|string',
            'meta_keywords' => 'sometimes|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $data = $validator->validated();

            $puckData = $this->contentTransformation->htmlToPuck(
                $data['content'],
                [
                    'title' => $data['title'] ?? '',
                    'meta_description' => $data['meta_description'] ?? '',
                    'meta_keywords' => $data['meta_keywords'] ?? '',
                ]
            );

            // Ensure all components have IDs
            $puckData = $this->puckValidation->ensureComponentIds($puckData);

            return response()->json([
                'success' => true,
                'data' => [
                    'puck_data' => $puckData,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to convert HTML to Puck: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Convert Puck data to HTML
     */
    public function convertPuckToHtml(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), $this->puckValidation->getPuckValidationRules());

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $data = $validator->validated();

            // Validate and ensure component IDs
            $puckData = $this->puckValidation->validateAndEnsureIds($data['puck_data']);

            $html = $this->contentTransformation->puckToHtml($puckData);
            $seoData = $this->contentTransformation->extractSeoData($puckData);

            return response()->json([
                'success' => true,
                'data' => [
                    'html' => $html,
                    'seo_data' => $seoData,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to convert Puck to HTML: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Switch editor type for a page
     */
    public function switchEditorType(Request $request, int $id): JsonResponse
    {
        $page = CmsPage::find($id);

        if (!$page) {
            return response()->json([
                'success' => false,
                'message' => 'Page not found'
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'editor_type' => 'required|in:html,puck',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $newEditorType = $request->input('editor_type');

            if ($page->switchEditorType($newEditorType)) {
                $page->updated_by = $request->user()->id;
                $page->save();
                $page->load(['creator:id,name', 'updater:id,name']);

                return response()->json([
                    'success' => true,
                    'message' => "Editor type switched to {$newEditorType} successfully",
                    'data' => [
                        'page' => $page,
                    ]
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to switch editor type'
                ], 500);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to switch editor type: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate Puck data structure
     */
    public function validatePuckData(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), $this->puckValidation->getPuckValidationRules());

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $data = $validator->validated();

            // Validate and ensure component IDs
            $validatedPuckData = $this->puckValidation->validateAndEnsureIds($data['puck_data']);

            return response()->json([
                'success' => true,
                'message' => 'Puck data is valid',
                'data' => [
                    'puck_data' => $validatedPuckData,
                    'is_valid' => true,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid Puck data: ' . $e->getMessage(),
                'data' => [
                    'is_valid' => false,
                ]
            ], 422);
        }
    }
}
